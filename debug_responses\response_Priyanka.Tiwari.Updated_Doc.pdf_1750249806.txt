=== RAW RESPONSE ===
Length: 4271
Source: Priyanka.Tiwari.Updated_Doc.pdf
Timestamp: 1750249806.2849832
=== CONTENT ===
```json
{
    "name": "<PERSON><PERSON><PERSON>",
    "email": "<EMAIL>",
    "phone": "+91 - 7415479050",
    "education": [
        {
            "degree": "Master of Computer Science",
            "institution": "Awadhesh Pratap singh University",
            "year": "2017 – First Class"
        },
        {
            "degree": "Bachelor of Science [ PCM]",
            "institution": "Awadhesh Pratap singh University",
            "year": "2015 – Second Class"
        }
    ],
    "skills": [
        ".Net",
        ".Net Core",
        "OOP Concept",
        "MVC",
        "Core MVC",
        "HTML",
        "Salesforce Cloud",
        "JavaScript",
        "TypeScript",
        "SQL Server",
        "LINQ",
        "Telerik Control",
        "Fresh MVVM",
        "Requirement Gathering",
        "Analysis",
        "Agile methodology",
        "GitHub",
        "Tortoise git",
        "Debugging",
        "Unit testing – NUnit",
        "Swagger",
        "Soap UI",
        "Solid Principle",
        "Design Pattern",
        "TeamCity",
        "Fiddler",
        "Angular"
    ],
    "experience": [
        {
            "company_name": "Cognizant, Pune",
            "role": "Project Associate",
            "duration": "Sep 2022 – Current",
            "key_responsibilities": "Developing and maintaining applications using .NET technologies. Participating in the SDLC, including requirements gathering, design, development, testing, and deployment. Collaborating with cross-functional teams to ensure project success. Supporting production environments and troubleshooting bugs. Executing responsibilities of project development, deployment, and deliveries. Providing support during production rotations and bug fixes."
        },
        {
            "company_name": "Capgemini, Pune",
            "role": "Software Developer",
            "duration": "Feb 2021 – Sep 2022",
            "key_responsibilities": "Developing and implementing software solutions using .NET framework.  Working on various projects including Sales Invoicing and Amadeus account. Designing and implementing API's and web applications.  Troubleshooting and debugging applications. Collaborating with team members to meet project goals."
        },
        {
            "company_name": "Winlancer Technology, Indore",
            "role": "Dot Net Developer",
            "duration": "August 2019 – Feb 2021",
            "key_responsibilities": "Developing and maintaining applications using .NET technologies. Implementing features and fixing bugs. Contributing to code reviews. Working with team members to deliver projects on time and within budget."
        },
        {
            "company_name": "Solmeyvn, Jabalpur",
            "role": "Developer",
            "duration": "Jan 2018 – July 2019",
            "key_responsibilities": "Developing and maintaining web applications using Asp.Net. Designing and implementing features and fixing bugs. Collaborating with team members to meet project goals."
        }
    ],
    "projects": [
        {
            "name": "Trafigura Account (WPF using C#, Reactive Extension, SOAP Service)",
            "description": "Developed a WPF application using C# to handle sales invoicing. This included implementing a Reactive Extension for asynchronous operations and a SOAP Service for communication. The project involved designing and building a UI for creating and managing various invoice types such as Provisional, Final, Proforma, Service, CN, and DN."
        },
        {
            "name": "Amadeus Account (Sales Force)",
            "description": "Developed a Sales Force application to manage sales operations. This project focused on replacing SOQL queries with selector and accelerator pattern in APEX pages."
        },
        {
            "name": "Amadeus Account (Using Sales force Utility)",
            "description": "Developed a solution to download data and upload it to another server using Sales force Utility."
        },
        {
            "name": "Albi Project (.Net Core Api’s and Web application)",
            "description": "Developed a .Net Core API's and Web application for this project included User panel and Admin panel where we are added new user ,login, Schedule