=== RAW RESPONSE ===
Length: 3608
Source: AniketResume #.Net.pdf
Timestamp: 1750249511.594435
=== CONTENT ===
```json
{
  "name": "<PERSON><PERSON><PERSON> Nikam",
  "email": "<EMAIL>",
  "phone": "+919096848240",
  "education": [
    {
      "degree": "BE in Electronics & Telecommunications",
      "institution": "Dr D Y Patil Institute of Technology Pune",
      "year": "Aug 2018 - Aug 2022"
    }
  ],
  "skills": [
    "C#",
    "ASP.NET",
    "MVC",
    "WEB API",
    "LINQ",
    "ADO.NET",
    "Entity Framework",
    "HTML",
    "CSS",
    "JavaScript",
    "Bootstrap",
    "Power Platform",
    "Ui Path",
    "Power Automate",
    "Win Form",
    "MS SQL",
    "PLSQL"
  ],
  "experience": [
    {
      "company_name": "I9 Innovations and Educations Pvt Ltd",
      "role": ".Net Developer",
      "duration": "Dec 2023 - Present",
      "key_responsibilities": "Working on Hospital Management System using .NET Framework 4.5. Utilized ASP.NET, C#, and MS SQL to design and optimize functionalities, contributing to increased operational efficiency. Designed and implemented stored procedures in Microsoft SQL Server to support efficient data retrieval and manipulation within the Hospital Management System. Conducted thorough testing and debugging procedures to ensure the reliability and accuracy of the Hospital Management System. Utilized software developer expertise to develop products throughout the software lifecycle to boost business efficiency, from ideation and requirements definition through to development and successful deployment."
    },
    {
      "company_name": "Atos Syntel Pvt Ltd (Eviden)",
      "role": "Associate Consultant",
      "duration": "Aug 2022 - Aug 2023",
      "key_responsibilities": "I worked on .Net projects utilizing ASP.NET MVC to gain practical experience and enhance my technical skills. Collaborated with team members to brainstorm ideas and contribute to the overall project vision. Continuously learned and adapted to new technologies and best practices in NET development. Designed and implemented stored pro cedures in Microsoft SQL Server. Design, develop, and maintain RPA solutions using Power Automate and also make applications using Power Apps. Hands -on experience in Power Platform. Identify and automate repetitive manual tasks and processes to improve efficiency. Create and configure automation workflows, including data integration and validation. Maintain comprehensive documentation of code, configurations, and automation workflows."
    }
  ],
  "projects": [],
  "certifications": [
    "Microsoft Certified Power Platform Fundamentals",
    "Microsoft Certified Azure Fundamentals",
    "ASP .NET MVC Certification Training"
  ],
  "domain_of_interest": [
    "Exploring new technologies",
    "Reading news",
    "Travelling to new place",
    "Swimming"
  ],
  "languages_known": [],
  "achievements": [],
  "publications": [],
  "volunteer_experience": [],
  "references": [],
  "summary": "A result-oriented IT Professional with over 1.5 years of experience in application development & enhancement (.NET Technologies) who aspires to achieve individual and organizational excellence through constant learning, commitment, and hard work. My expert ise includes all development stages ,from initial analysis through design and execution. Able to effectively self -manage during independent projects, as well as collaborate as part of a productive team. My ability to collaborate well enables me to work eff ectively with clients to identity objectives and requirements for application.",
  "personal_projects": [],
  "social_media": [
    "www.linkedin.com/in/aniketnikam2305",
    "www.linkedin.com/in/aniketnikam2305"
  ]
}
```