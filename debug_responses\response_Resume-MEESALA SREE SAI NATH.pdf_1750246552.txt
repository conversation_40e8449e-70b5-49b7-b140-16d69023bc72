=== RAW RESPONSE ===
Length: 3959
Source: Resume-MEESALA SREE SAI NATH.pdf
Timestamp: 1750246552.1939492
=== CONTENT ===
```json
{
    "name": "<PERSON>esala Sr<PERSON>",
    "email": null,
    "phone": null,
    "education": [
        {
            "degree": "Bachelor of Technology (Computer Science)",
            "institution": "Newton School of Technology , Rishihood University",
            "year": "2023 - 2027"
        },
        {
            "degree": "Intermediate (Class XII)",
            "institution": "Sri Chaitanya Junior College",
            "year": "2020 - 2022"
        },
        {
            "degree": "Matriculation (Class X)",
            "institution": "Kendriya Vidyalaya No-1 , Air Force Academy",
            "year": "2019 - 2020"
        }
    ],
    "skills": [
        "Java",
        "JavaScript",
        "CSS",
        "HTML",
        "Python",
        "React",
        "Mongo DB",
        "Django",
        "Express JS",
        "NodeJS",
        "Prisma ORM",
        "MySQL",
        "Tailwind",
        "Hadoop HDFS",
        "Data Structure"
    ],
    "experience": [
        {
            "company_name": "Spectacom Global Gurugram,Haryana",
            "role": "SDE INTERN",
            "duration": "January 2025 - Present",
            "key_responsibilities": "Developed a leaderboard system in Django , supporting regional and individual rankings . \nIntegrated detailed participant timing records with numerical insights for performance tracking. \nOptimized Django ORM queries for ecient leaderboard updates and retrieval."
        }
    ],
    "projects": [
        {
            "name": "Laundry Pro",
            "description": "Laundry Pro simplies laundry services with an ecient platform for students to place and track orders \nand admins to manage operations. \nTech Stack: Built with Node.js, Express, Prisma, SQL. \nFeatures: \n{Secure login for students and admins. \n{Admin tools for order status updates and record management."
        },
        {
            "name": "Fabrix",
            "description": "Developed a dynamic, simulated e-commerce platform with secure user authentication and product \nbrowsing using dummy data for a modern, responsive shopping experience. \nTech Stack: Built with React ,Tailwind CSS , and integrated Razorpay for secure checkout. \nFeatures: \n{Secure user authentication with personalized features. \n{Product browsing and ltering using dummy data, Shopping cart management with Razorpay integration for secure checkout."
        },
        {
            "name": "Crypto - manager",
            "description": "Created a web application for managing and tracking cryptocurrency investments with real-time data and \na responsive, interactive user interface. \nTech Stack: Built with HTML ,CSS and JavaScript. \nFeatures: \n{Real-time API integration to fetch live cryptocurrency data and market trends. \n{Simulated trading for buying, selling, and holding cryptocurrencies in a risk-free environment.."
        }
    ],
    "certifications": [
        "Python for Beginners - Newton School (2024)"
    ],
    "domain_of_interest": [
        "Data Structure"
    ],
    "languages_known": [
        "Java",
        "JavaScript",
        "Python",
        "HTML",
        "CSS"
    ],
    "achievements": [
        "Reliance Foundation Undergraduate Scholar",
        "Ranked in the top-3 in the Stealth Fire Hackathon conducted by Newton School of Technology",
        "Earned the Rajya Puraskar award inScouts and Guides after completing rigorous training and Community service",
        "Qualied for the Semi-Finals inBadminton at the University Sports Fest"
    ],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": "Full Stack Developer skilled in React, Tailwind CSS, Node.js, Express, Django, and MySQL, with experience in scalable APIs\nand dynamic UIs. Passionate about learning and building high-performing web applications.",
    "personal_projects": [],
    "social_media": [
        "null",
        "null"
    ]
}
```