=== RAW RESPONSE ===
Length: 2268
Source: AkshayChawaleCVC.pdf
Timestamp: 1750249461.1908648
=== CONTENT ===
```json
{
    "name": "Aksh A Y G.Ch A Wale",
    "email": "<NAME_EMAIL>",
    "phone": "+918793424090",
    "education": [],
    "skills": [
        "Html",
        "Css",
        "Bootsrap",
        "Javascript",
        "Jquery",
        "MS-Ms-Sql Server",
        "Postgr esql",
        "Microsoft Excel",
        "Microsoft Word",
        "Databases",
        "Asp.net",
        "C#",
        "Asp.net MVC",
        "Asp.net Core",
        "OOPS",
        "LINQ",
        "EntityF r amework",
        "GI T",
        "GI THUB",
        "Web APIs",
        "WPF",
        "WCF",
        "Microsoft Azure",
        "Windows operating system",
        "Linux operating system",
        "Linux commands",
        "Hardware and Support for Client Queries"
    ],
    "experience": [
        {
            "company_name": "pvt Ltd",
            "role": "Asp.net",
            "duration": null,
            "key_responsibilities": "Details regarding responsibilities and achievements are not available in the resume."
        }
    ],
    "projects": [],
    "certifications": [
        "Python for Beginners - Newton School (2024)"
    ],
    "domain_of_interest": [
        "null"
    ],
    "languages_known": [
        "Mar athi",
        "Hindhi",
        "English"
    ],
    "achievements": [
        "null"
    ],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": "T o join in a pr ogr essiv e company wher e I can apply my skills & logical abilities which Contributes t o the personal and company gr owth",
    "personal_projects": [],
    "social_media": [],
    "skills": [
        "Html",
        "Css",
        "Bootsrap",
        "Javascript",
        "Jquery",
        "MS-Ms-Sql Server",
        "Postgr esql",
        "Microsoft Excel",
        "Microsoft Word",
        "Databases",
        "Asp.net",
        "C#",
        "Asp.net MVC",
        "Asp.net Core",
        "OOPS",
        "LINQ",
        "EntityF r amework",
        "GI T",
        "GI THUB",
        "Web APIs",
        "WPF",
        "WCF",
        "Microsoft Azure",
        "Windows operating system",
        "Linux operating system",
        "Linux commands",
        "Hardware and Support for Client Queries"
    ]
}
```