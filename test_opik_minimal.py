#!/usr/bin/env python3
"""
Minimal test to verify Opik integration works.
"""

import os

def test_minimal_opik():
    """Test minimal Opik setup."""
    
    print("🧪 Minimal Opik Test")
    print("=" * 30)
    
    try:
        # Clear any existing proxy environment variables that might interfere
        proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY', 'all_proxy']
        for var in proxy_vars:
            if var in os.environ:
                print(f"🔧 Clearing proxy variable: {var}")
                del os.environ[var]
        
        # Set Opik configuration
        os.environ["OPIK_API_KEY"] = "FxxYR5XJJ9lACASGJ677QE3a7"
        os.environ["OPIK_PROJECT_NAME"] = "Gemma api"
        os.environ["OPIK_WORKSPACE"] = "gaja-prasanth"
        os.environ["OPIK_URL_OVERRIDE"] = "https://www.comet.com/opik/api"
        os.environ["OPIK_CHECK_TLS_CERTIFICATE"] = "false"
        
        print("✅ Environment variables set")
        
        # Import and test Opik
        import opik
        print("✅ Opik imported successfully")
        
        # Try to create client
        client = opik.Opik()
        print("✅ Opik client created successfully")
        
        # Test decorator
        from opik import track
        
        @track(name="test_minimal", project_name="Gemma api")
        def test_function():
            return "Hello from Opik!"
        
        result = test_function()
        print(f"✅ Decorator test successful: {result}")
        
        # Flush traces
        client.flush()
        print("✅ Traces flushed")
        
        print("\n🎉 All tests passed! Opik is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_minimal_opik()
    if success:
        print("\n📈 Check your Opik dashboard at: https://www.comet.com/opik")
        print("   Workspace: gaja-prasanth")
        print("   Project: Gemma api")
    else:
        print("\n❌ Opik integration failed. Check the error messages above.")
