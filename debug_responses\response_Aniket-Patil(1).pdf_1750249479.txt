=== RAW RESPONSE ===
Length: 4627
Source: Aniket-Patil(1).pdf
Timestamp: 1750249479.4211287
=== CONTENT ===
```json
{
    "name": "<PERSON>iket Patil",
    "email": "<EMAIL>",
    "phone": "+917757815203",
    "education": [
        {
            "degree": "B.E. (Computer Science)",
            "institution": "R. H. Sapat college of engineering",
            "year": "2017 – 2021"
        },
        {
            "degree": "HSC",
            "institution": "M. S. Gosavi, Junior college",
            "year": "2017"
        },
        {
            "degree": "SSC",
            "institution": "New Maratha Highschool",
            "year": "2015"
        }
    ],
    "skills": [
        ".NET",
        "Blazor",
        "JavaScript",
        "jQuery",
        "HTML",
        "CSS",
        "Bootstrap",
        "MS-SQL Server",
        "Architectural Patterns",
        "Programming"
    ],
    "experience": [
        {
            "company_name": "The BlueFlame Labs",
            "role": "Technical Consultant",
            "duration": "11/2022 – present",
            "key_responsibilities": "Created clear project documentation to help teams to understand project requirements and processes better. Designed project structures to make workflows smoother and resources more efficient. Used .NET Technology Stack effectively to drive project success. Developed interactive web applications and single-page applications using Blazor for better user experiences. Optimized database queries and schema design for improved performance and scalability."
        },
        {
            "company_name": "Cognizant Technology Solutions",
            "role": "Jr. Software Engineer",
            "duration": "09/2021 – 11/2022",
            "key_responsibilities": "Provided dedicated support for implemented applications, focusing on continuous process improvement to consistently meet and exceed client expectations. Collaborated with cross-functional teams to identify and implement enhancements, ensuring optimal performance and user satisfaction. Provided support in documentation efforts, including updating technical documentation and user manuals. Collaborated to troubleshoot and debug issues in existing codebase, gaining proficiency in debugging tools and techniques."
        }
    ],
    "projects": [
        {
            "name": "Project 1",
            "description": "Developed a web-based application for a leading life insurance company, facilitating the provision of life insurance benefits to individuals. Application is an end-to-end flow from collecting the individual's data, processing it, and based on calculated eligibility, provide a benefit to individuals. Additionally, developed different separate applications for the different teams within the client's organization, enhancing task efficiency and workflow management. Responsibilities Collaborated with the project team to understand client requirements and convert them into technical solutions. Designed and implemented robust backend functionalities using .NET technologies to handle data processing and eligibility calculations. Conducted thorough testing and debugging to ensure the reliability and accuracy of the applications. Provided ongoing support and maintenance, addressing any issues or enhancements required by the client."
        },
        {
            "name": "Project 2",
            "description": "Developed a web-based application for a prominent US-based legal firm, comprising two distinct applications. The first application facilitated an end-to-end process for managing legal cases, including collecting information on legal situations, involved parties, and required grants. The second application, previously a Salesforce-based solution, served as an internal tool for legal firm employees. The client sought to migrate from Salesforce to an open-source platform while maintaining identical functionalities and UI to the Salesforce sandbox environment. Responsibilities Worked as Full Stack Developer and involved in full lifecycle of application development. Implemented the backend app with C#, .NET Core API and used MS SQL Server as database. Used Blazor.NET with WASM as a Front-end technology. Designed front-end components with Radzen, HTML, Bootstrap, CSS, JS. Involved in Application deployment activity. Performed Unit Testing."
        }
    ],
    "certifications": [
        "Python for Beginners - Newton School (2024)"
    ],
    "domain_of_interest": [
        "Software Development",
        "Web Technologies"
    ],
    "languages_known": [
        "MVC",
        "C#"
    ],
    "achievements": [
        "Awarded for excellent performance in academics"
    ],
    "publications": [],
    "