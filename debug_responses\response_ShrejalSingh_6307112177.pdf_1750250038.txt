=== RAW RESPONSE ===
Length: 3741
Source: ShrejalSingh_6307112177.pdf
Timestamp: 1750250038.9598718
=== CONTENT ===
```json
{
    "name": "<PERSON><PERSON><PERSON><PERSON> <PERSON>",
    "email": "<EMAIL>",
    "phone": "+918004811580",
    "education": [
        {
            "degree": "BE, Computer Science Engineering",
            "institution": "SLIET UNIVERSITY",
            "year": "07/2022"
        },
        {
            "degree": "Diploma Computer Science Engineering",
            "institution": "Sevdie Institute of Management & Technology",
            "year": "10/2018"
        }
    ],
    "skills": [
        "NET",
        "SQL",
        "C#",
        "EDI",
        "VB.NET",
        "JavaScript",
        "Java",
        "PowerShell Scripting",
        "LINQ",
        "ASP.NET Core",
        "MVC",
        "ReactJS",
        "CSS",
        "Rest API",
        "Webhook",
        "WPF",
        "Mongo DB",
        "MySQL",
        "ORACLE",
        "MSSQL",
        "T-SQL",
        "GIT",
        "TFS",
        "SDLC",
        "Agile",
        "Design Pattern"
    ],
    "experience": [
        {
            "company_name": "Ojcommerce",
            "role": "Software Developer",
            "duration": "September 2022 - PRESENT",
            "key_responsibilities": "Implemented multi -tier (DB, services and web) applications, and have tested and perform fix & documentation\nEDI Support & Integration to process purchase orders, tracking\nnumbers and invoices with its Daily Monitoring for Vendor,\nHandled production Issues, Vendor Onboarding and AS2 certificate Change.\nDevelopment of Application using ASP.Net, and deployments of DLL for production and dev server,\nWrote SQL functions, triggers and Stored Procedures.\nWorked on implementation of new UI and API Integration for company website.\nPerforming daily code reviews, improved performance and code quality"
        },
        {
            "company_name": "Cetpa Info Tech Pvt Ltd",
            "role": "Full Stack Developer",
            "duration": "October 2020 - April 2021",
            "key_responsibilities": "Developed A (Coffee Corner) E -Commerce website using HTML, CSS, JavaScript, React.js, MongoDb & JQuery\nDeveloped, tested, debugged and implemented code for web -based applications."
        },
        {
            "company_name": "Softpro India Computer Technologies P.",
            "role": "AWARDS",
            "duration": "June 2017 – September 2017",
            "key_responsibilities": "Developed .NET applications using C#, ASP.NET, HTML, and JavaScript.\nInvolved in complete SDLC including analysis, design, coding, testing and deployment of various projects."
        }
    ],
    "projects": [
        {
            "name": "VENDOR ONBOARDING",
            "description": "Standalone application to test EDI Files"
        },
        {
            "name": "OJCOMMERCE WEBSITE 3.0",
            "description": "Webhook"
        },
        {
            "name": "PREDICTION OF TARIFF RATE",
            "description": "Prediction Tariff Rate between Two Cities. This project was assigned by TCI through SIH Hackathon 2020 held by MHRD, India. Awarded as OUTSTANDING PERFORMER"
        },
        {
            "name": "IBN NEWS CHANNEL",
            "description": "MVC, ASP.Net, Angular, SQL"
        },
        {
            "name": "CYST BEING",
            "description": "A website spreads awareness about PCOS/PCOD & detect the level of the disorder using React_JS, Flask, ML, Express."
        }
    ],
    "certifications": [
        "Python for Beginners - Newton School (2024)",
        "NET, SQL, C#, EDI, VB.NET, JavaScript, Java, PowerShell Scripting, LINQ"
    ],
    "domain_of_interest": [
        "Software Development"
    ],
    "languages_known": [
        "English",
        "Hindi"
    ],
    "achievements": [
        "