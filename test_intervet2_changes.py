#!/usr/bin/env python3
"""
Test script to verify the intervet2 changes:
1. Uses hybrid_resume for resume parsing
2. New weightage system (Skills 35%, Experience 35%, Reliability 15%, Certifications 5%, Location 5%, <PERSON> Mater 5%)
3. Removed subjective skills matching
"""

import requests
import json
import os
import time
from pathlib import Path

# Configuration
API_BASE_URL = "http://localhost:8000"
TEST_RESUME_PATH = "resumes for testing/Resume-Raman Lu<PERSON>ch.pdf"
TEST_JD_PATH = "test_jd.docx"

def test_intervet2_changes():
    """Test the intervet2 endpoint with new changes."""
    
    print("🧪 Testing Intervet2 Changes")
    print("=" * 50)
    
    # Check if test files exist
    if not os.path.exists(TEST_RESUME_PATH):
        print(f"❌ Test resume not found: {TEST_RESUME_PATH}")
        return False
    
    if not os.path.exists(TEST_JD_PATH):
        print(f"❌ Test JD not found: {TEST_JD_PATH}")
        return False
    
    print(f"📄 Using test resume: {TEST_RESUME_PATH}")
    print(f"📋 Using test JD: {TEST_JD_PATH}")
    
    try:
        # Test the intervet2 endpoint
        print("\n🔍 Testing /intervet2 endpoint with new changes...")
        
        with open(TEST_RESUME_PATH, 'rb') as resume_file, open(TEST_JD_PATH, 'rb') as jd_file:
            files = {
                'resume_file': (os.path.basename(TEST_RESUME_PATH), resume_file, 'application/pdf'),
                'jd_file': (os.path.basename(TEST_JD_PATH), jd_file, 'text/plain')
            }
            
            start_time = time.time()
            response = requests.post(f"{API_BASE_URL}/intervet2", files=files, timeout=300)
            end_time = time.time()
            
            print(f"⏱️  Request completed in {end_time - start_time:.2f} seconds")
            print(f"📊 Response status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Intervet2 processing successful!")
                
                # Check if hybrid resume parsing was used
                if "resume_data" in result:
                    resume_data = result["resume_data"]
                    extraction_method = resume_data.get("extraction_method", "unknown")
                    print(f"📊 Resume extraction method: {extraction_method}")
                    
                    if "hybrid" in extraction_method.lower():
                        print("✅ Hybrid resume parsing confirmed!")
                    else:
                        print("⚠️  Expected hybrid parsing, but got:", extraction_method)
                
                # Check the new scoring system
                if "candidate_job_fit" in result:
                    fit_data = result["candidate_job_fit"]
                    total_score = fit_data.get("total_score", 0)
                    fit_category = fit_data.get("fit_category", "Unknown")
                    detailed_scores = fit_data.get("detailed_scores", {})
                    
                    print(f"\n📊 Candidate Job Fit Analysis:")
                    print(f"🎯 Total Score: {total_score}/100")
                    print(f"📈 Fit Category: {fit_category}")
                    
                    print(f"\n📋 Detailed Scores (New Weightage System):")
                    
                    # Expected categories with their max scores
                    expected_categories = {
                        "skills_match_direct": 35,      # 35% weight
                        "experience_match": 35,         # 35% weight  
                        "reliability": 15,              # 15% weight
                        "certifications": 5,            # 5% weight
                        "location_match": 5,            # 5% weight
                        "alma_mater": 5                 # 5% weight
                    }
                    
                    total_expected = sum(expected_categories.values())
                    print(f"📊 Expected total max score: {total_expected}")
                    
                    for category, max_score in expected_categories.items():
                        actual_score = detailed_scores.get(category, 0)
                        percentage = (actual_score / max_score) * 100 if max_score > 0 else 0
                        print(f"   • {category.replace('_', ' ').title()}: {actual_score}/{max_score} ({percentage:.1f}%)")
                    
                    # Check that subjective skills is NOT present
                    if "skills_match_subjective" in detailed_scores:
                        print("❌ ERROR: Subjective skills matching should be removed!")
                    else:
                        print("✅ Subjective skills matching correctly removed")
                    
                    # Check that academic_match is NOT present
                    if "academic_match" in detailed_scores:
                        print("❌ ERROR: Academic match should be removed!")
                    else:
                        print("✅ Academic match correctly removed")
                
                # Check interview questions
                if "interview_questions" in result:
                    questions = result["interview_questions"]
                    total_questions = sum(len(q) for q in questions.values() if isinstance(q, list))
                    print(f"\n❓ Interview Questions Generated: {total_questions} total")
                    
                    for category, q_list in questions.items():
                        if isinstance(q_list, list):
                            print(f"   • {category.replace('_', ' ').title()}: {len(q_list)} questions")
                
                print(f"\n📊 Processing Summary:")
                print(f"   • Resume parsing: {'Hybrid method' if 'hybrid' in extraction_method.lower() else 'Standard method'}")
                print(f"   • Scoring system: New 6-category weightage")
                print(f"   • Total score: {total_score}/100")
                print(f"   • Fit assessment: {fit_category}")
                
                return True
            else:
                print(f"❌ Request failed with status {response.status_code}")
                print(f"Error: {response.text}")
                return False
                
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to API. Make sure the server is running on localhost:8000")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def check_api_health():
    """Check if the API is running and healthy."""
    
    try:
        response = requests.get(f"{API_BASE_URL}/", timeout=10)
        if response.status_code == 200:
            print("✅ API is running and healthy")
            return True
        else:
            print(f"❌ API health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Could not reach API: {e}")
        return False

def main():
    """Main test function."""
    
    print("🧪 Intervet2 Changes Test Suite")
    print("=" * 50)
    print("Testing:")
    print("1. ✅ Hybrid resume parsing integration")
    print("2. ✅ New weightage system (Skills 35%, Experience 35%, Reliability 15%, etc.)")
    print("3. ✅ Removal of subjective skills matching")
    print("4. ✅ Removal of academic match scoring")
    print("=" * 50)
    
    # Check API health
    if not check_api_health():
        print("\n💡 Make sure to start the API server first:")
        print("   python main.py")
        return
    
    # Test intervet2 changes
    success = test_intervet2_changes()
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"✅ Intervet2 changes: {'PASSED' if success else 'FAILED'}")
    
    if success:
        print("\n🎉 All tests passed! Intervet2 changes are working correctly.")
        print("\n📋 Changes Verified:")
        print("   ✅ Hybrid resume parsing implemented")
        print("   ✅ New 6-category scoring system active")
        print("   ✅ Skills: 35%, Experience: 35%, Reliability: 15%")
        print("   ✅ Certifications: 5%, Location: 5%, Alma Mater: 5%")
        print("   ✅ Subjective skills matching removed")
        print("   ✅ Academic match scoring removed")
        print("   ✅ Total scoring out of 100 points")
    else:
        print("\n❌ Some tests failed. Check the error messages above.")

if __name__ == "__main__":
    main()
