=== RAW RESPONSE ===
Length: 4486
Source: <PERSON>ks<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (1).pdf
Timestamp: 1750249447.9201026
=== CONTENT ===
```json
{
    "name": "<PERSON><PERSON><PERSON><PERSON> Pat<PERSON>",
    "email": "<EMAIL>",
    "phone": "+919765239795",
    "education": [
        {
            "degree": "MBA (Information Technology)",
            "institution": "Savitribai Phule Pune University",
            "year": "2020/05"
        },
        {
            "degree": "BCS",
            "institution": "Shivaji University Kolhapur",
            "year": "2018/05"
        }
    ],
    "skills": [
        "HTML5",
        "CSS",
        "Bootstrap",
        "Angular 13+",
        "Web API",
        "LINQ",
        "Javascript",
        "Typescript",
        "Asp.net Core",
        "C#",
        ".NET Framework",
        "MS SQL Server"
    ],
    "experience": [
        {
            "company_name": "OmniXM (Every Experience Matters)",
            "role": "Software Developer",
            "duration": "2022/02 – 2024/02",
            "key_responsibilities": "Developed web applications using technologies HTML, CSS, Angular 13+, Asp.net core web API and MySQL. Extensive hands-on experience in designing and building web applications. Expertise in Angular services, Components, routing. Implemented Lazy loading and routing. Good experience in developing applications using Angular Material. Possess good experience of Typescript. Building SPA's creating components and Forms(Template driven and Reactive forms). Experience in creating WEB API, in .net core.API which communicated data using HTTP protocol. Strong knowledge of OOP concepts. Experience In Using Configuration Management tools Git. Experience in using POSTMAN. Experience in executing projects using SDLC. Experience in Testing."
        }
    ],
    "projects": [
        {
            "name": "BreakRoom",
            "description": "BreakRoom encompasses multiple modules to streamline various aspects related to warehouse management, service delivery, water distribution, beverage provision, vehicle tracking, checklist submission, and order processing. Technologies used : Angular 14, asp.net core, WEB API, LINQ,SQL Server. Major contribution: Develop new features within the application, focusing on enhancing user experience and functionality. Analyze and resolve technical and application issues, including debugging and troubleshooting to ensure smooth operation. Collaborate with the team to brainstorm and implement innovative solutions to improve the overall performance and efficiency of the platform."
        },
        {
            "name": "FreeFood",
            "description": "By integrating these modules, FreeFood provides a comprehensive solution for managing food service operations, from menu creation and scheduling to reporting and analysis, enhancing efficiency and customer satisfaction. Technologies used : Angular 14, asp.net core, WEB API, LINQ,SQL Server. Major contribution: Develop new features within the application, focusing on enhancing user experience and functionality. Analyze and resolve technical and application issues, including debugging and troubleshooting to ensure smooth operation. Collaborate with the team to brainstorm and implement innovative solutions to improve the overall performance and efficiency of the platform."
        }
    ],
    "certifications": [
        "Python for Beginners - Newton School (2024)"
    ],
    "domain_of_interest": [
        "Web Development",
        "Software Engineering"
    ],
    "languages_known": [
        "English",
        "Hindi",
        "Marathi"
    ],
    "achievements": [
        "Experience in executing projects using SDLC.",
        "Experience in Testing."
    ],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": "2+ years of experienced as a developer using technologies HTML, CSS, Angular 13+, Asp.net core web API and MySQL. Extensive hands-on experience in designing and building web application. Expertise in Angular services, Components, routing. Implemented Lazy loading and routing. Good experience in developing applications using Angular Material. Possess good experience of Typescript. Building SPA's creating components and Forms(Template driven and Reactive forms). Experience in creating WEB API, in .net core.API which communicated data using HTTP protocol. Strong knowledge of OOP concepts. Experience In Using Configuration Management tools Git. Experience in using POSTMAN. Experience in executing projects using SDLC.",
    "personal_projects": [],
    "social_media": []
}
```