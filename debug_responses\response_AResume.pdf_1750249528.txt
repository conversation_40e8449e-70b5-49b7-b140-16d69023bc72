=== RAW RESPONSE ===
Length: 3951
Source: AResume.pdf
Timestamp: 1750249528.0380192
=== CONTENT ===
```json
{
    "name": "Abbas Bandeali Morve",
    "email": "<EMAIL>",
    "phone": "9881786703",
    "education": [
        {
            "degree": "Bachelor of Computer Application",
            "institution": "Poona College, Pune University, Pune, Maharashtra",
            "year": "2019"
        }
    ],
    "skills": [
        "VB.NET",
        "ADO.NET",
        "Core Java",
        "C#",
        ".NET Framework",
        "Visual Studio 2010/2015/2019",
        "jQuery",
        "JavaScript",
        "CSS",
        "AJAX",
        "XML",
        "HTML",
        "SQL Server"
    ],
    "experience": [
        {
            "company_name": null,
            "role": "Project",
            "duration": null,
            "key_responsibilities": "The software is designed keeping in mind the requirements of AKIS Police station CRIME DEPARTMENT MANAGEMENT system.The requirement of Police station was to have a easy to use GUI based CRIME DEPARTMENT SYSTEM.The Crime department System is a user friendly and low cost solution to their needs.This system can also be used by untrained.\nDocumented the System flow according to User Requirements.\nStoring the records related to the criminals, cases, complaint record and case history."
        },
        {
            "company_name": null,
            "role": "Project",
            "duration": null,
            "key_responsibilities": "This project is designed for WATER PURIFIER  MANAGEMENT SYSTEM. This will help to maintain the records of all customers, suppliers, products details ,availability and orders made by the customers and suppliers.Water Purifier Store Management System is useful for to maintain Customer Details, To Maintain Order Details, and To Maintain Supplier Details, to Stock Details etc.\nDocumented the System flow according to User Requirements.\nStore data in the form of table and edit and customer them as per requirements of the user.\nThis project is easy to operate and understood by the users."
        }
    ],
    "projects": [
        {
            "name": "CRIME MANAGEMENT SYSTEM",
            "description": "The software is designed keeping in mind the requirements of AKIS Police station CRIME DEPARTMENT MANAGEMENT system.The requirement of Police station was to have a easy to use GUI based CRIME DEPARTMENT SYSTEM.The Crime department System is a user friendly and low cost solution to their needs.This system can also be used by untrained."
        },
        {
            "name": "Water Purifier Management System",
            "description": "This project is designed for WATER PURIFIER  MANAGEMENT SYSTEM. This will help to maintain the records of all customers, suppliers, products details ,availability and orders made by the customers and suppliers.Water Purifier Store Management System is useful for to maintain Customer Details, To Maintain Order Details, and To Maintain Supplier Details, to Stock Details etc."
        }
    ],
    "certifications": [
        "Web Designing",
        "Software Testing"
    ],
    "domain_of_interest": [],
    "languages_known": [],
    "achievements": [
        "An effective communicator with strong analytical, interpersonal and problem solving abilities."
    ],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": "Computer applications graduate with strong programming skills in languages such as .net and Java.\nSeeking a challenging position in a reputable organization to expand and utilize my learning, skills and knowledge.\nTo work for my career growth and better prospects, which contribute to the productivity and profitability of the organization.\nDetail oriented with an analytical bent of mind and possess positive attitude.\nHave been proactive and focused as a student.\nProficiency at grasping new technical concepts quickly & utilising it in a productive manner.",
    "personal_projects": [],
    "social_media": []
}
```