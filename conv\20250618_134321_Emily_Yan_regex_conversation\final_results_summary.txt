================================================================================
FINAL EXTRACTION RESULTS SUMMARY
================================================================================
Extraction Method: regex
Completed: 2025-06-18T13:43:21.140002
Processing Time: 0.1219937801361084 seconds
Total LLM Calls: 0
Overall Confidence: 0.86
Sections Found: 5
================================================================================

SECTION EXTRACTION RESULTS:
----------------------------------------
BASIC_INFO: ✅ SUCCESS
  Confidence: 1.00
  Content Length: 12503 characters
  Preview: Emily Yan                                             14255433620    <EMAIL>         h...

BASIC_NAME: ✅ SUCCESS
  Confidence: 0.85
  Content Length: 4115 characters
  Preview: • Implemented AWS security best practices, utilizing IAM roles, AWS KMS for encryption, ensuring sec...

BASIC_EMAIL: ✅ SUCCESS
  Confidence: 0.95
  Content Length: 21 characters
  Content: <EMAIL>

BASIC_PHONE: ✅ SUCCESS
  Confidence: 0.90
  Content Length: 11 characters
  Content: 14255433620

SKILLS: ✅ SUCCESS
  Confidence: 0.60
  Content Length: 3785 characters
  Preview: Java
SQL
Python
JavaScript Technologies: RESTful APIs
Microservices
Kafka
RDBMS (Oracle
MySQL
Postgr...

================================================================================
All individual LLM calls are logged in separate files in this folder.
Check the numbered call files for detailed prompt/response logs.
