# Opik Integration for Gemma API

This document describes the comprehensive Opik integration implemented in the Gemma API for LLM observability, tracing, and metrics logging.

## Overview

[Opik](https://www.comet.com/docs/opik) is a powerful LLM observability platform by Comet that provides:
- **Comprehensive Observability**: Deep tracing of LLM calls, conversation logging, and agent activity
- **Advanced Evaluation**: Robust prompt evaluation and LLM as a Judge metrics
- **Production Monitoring**: High-volume trace ingestion and monitoring dashboards
- **Cost Tracking**: Token usage and cost monitoring across different models

## Configuration

The Opik integration is configured with hardcoded credentials in `main.py`:

```python
# Opik Configuration
os.environ["OPIK_API_KEY"] = "FxxYR5XJJ9lACASGJ677QE3a7"
os.environ["OPIK_PROJECT_NAME"] = "Gemma api"
os.environ["OPIK_WORKSPACE"] = "gaja-prasanth"
os.environ["OPIK_URL_OVERRIDE"] = "https://www.comet.com/opik/api"
os.environ["OPIK_CHECK_TLS_CERTIFICATE"] = "false"  # For Windows compatibility
```

### Safe Integration

The integration includes safe wrapper functions that ensure the API continues to work even if Opik fails to initialize:

- `safe_opik_track()` - Safe decorator that falls back to no-op if Opik is unavailable
- `safe_opik_update_trace()` - Safe trace updates with error handling
- `safe_opik_update_span()` - Safe span updates with error handling

## Features Implemented

### 1. LLM Call Tracing

Every LLM call through the `get_response()` function is automatically traced with:
- **Input/Output Logging**: Full prompt and response content
- **Metadata Tracking**: Model name, timeout, max tokens, processing time
- **Token Usage**: Evaluation count, prompt evaluation count, total duration
- **Error Handling**: Timeout and general error tracking with feedback scores

### 2. Pipeline Tracing

Major processing pipelines are tracked as separate traces:
- **Resume Processing Pipeline**: `parse_resume()` function
- **Job Description Processing Pipeline**: `parse_jd()` function  
- **Text Extraction**: `extract_text_from_file()` function
- **Resume Parsing**: `parse_resume_with_gemma()` function
- **JD Parsing**: `parse_jd_with_gemma()` function

### 3. API Endpoint Tracing

Key API endpoints are decorated with Opik tracking:
- `/resume` - Resume parsing endpoint
- `/jd_parser` - Job description parsing endpoint
- `/hybrid_resume` - Hybrid resume parsing endpoint

### 4. Feedback Scores and Quality Metrics

Automatic quality assessment with feedback scores:
- **Response Quality**: Based on response length and content
- **Processing Speed**: Performance metrics for optimization
- **Success/Failure**: Binary success indicators
- **Error Severity**: Categorized error severity levels

### 5. Metadata and Tags

Rich metadata and tagging system:
- **Tags**: Endpoint names, call types, model names
- **Metadata**: File information, processing times, pipeline stages
- **Context**: Source filenames, document types, processing methods

## Traced Functions

### Core LLM Function
```python
@track(name="llm_call", project_name="Gemma api")
def get_response(prompt, timeout_seconds, max_tokens, ...):
    # Comprehensive LLM call tracing with error handling
```

### Processing Pipelines
```python
@track(name="resume_processing_pipeline", project_name="Gemma api")
def parse_resume(resume_text, convert_skills_to_dict_format, source_filename):
    # Full resume processing pipeline tracing

@track(name="job_description_processing_pipeline", project_name="Gemma api")
def parse_jd(jd_text, source_filename):
    # Job description processing pipeline tracing
```

### API Endpoints
```python
@track(name="resume_endpoint", project_name="Gemma api")
async def parse_resume_endpoint(request, file):
    # Resume API endpoint tracing

@track(name="job_description_endpoint", project_name="Gemma api")
async def parse_jd_endpoint(file):
    # Job description API endpoint tracing
```

## Metrics Logged

### LLM Performance Metrics
- **Processing Time**: End-to-end LLM call duration
- **Token Usage**: Input and output token counts
- **Response Quality**: Heuristic-based quality scores
- **Success Rate**: Success/failure tracking

### Pipeline Metrics
- **Text Extraction Time**: Document processing duration
- **Parsing Accuracy**: Confidence scores from parsing
- **Error Rates**: Categorized error tracking
- **File Processing**: File size, type, and extraction method

### Quality Scores
- **Response Quality**: 0.0-1.0 based on response characteristics
- **Processing Speed**: 0.0-1.0 based on processing time
- **Success Rate**: Binary success/failure indicators
- **Error Severity**: 0.0-1.0 error severity classification

## Dashboard Views

In your Opik dashboard, you'll see:

### Traces View
- **LLM Calls**: Individual LLM interactions with full context
- **Pipeline Execution**: Multi-step processing workflows
- **API Requests**: End-to-end API request tracing

### Metrics View
- **Performance Trends**: Processing time and success rate trends
- **Quality Metrics**: Response quality and accuracy over time
- **Error Analysis**: Error frequency and severity analysis

### Cost Tracking
- **Token Usage**: Input/output token consumption
- **Model Costs**: Cost breakdown by model and endpoint
- **Usage Patterns**: API usage patterns and optimization opportunities

## Testing the Integration

Run the test script to verify Opik integration:

```bash
python test_opik_integration.py
```

This will:
1. Test resume parsing with Opik tracing
2. Test job description parsing with Opik tracing
3. Verify all traces are properly logged
4. Check dashboard connectivity

## Accessing Your Dashboard

1. **Login**: Visit [https://www.comet.com/opik](https://www.comet.com/opik)
2. **Project**: Navigate to "Gemma api" project
3. **Traces**: View all LLM calls and pipeline executions
4. **Metrics**: Analyze performance and quality trends
5. **Experiments**: Set up evaluation experiments

## Benefits

### Development
- **Debugging**: Detailed trace information for troubleshooting
- **Performance**: Identify bottlenecks and optimization opportunities
- **Quality**: Monitor response quality and accuracy

### Production
- **Monitoring**: Real-time monitoring of LLM performance
- **Alerting**: Set up alerts for errors or performance degradation
- **Analytics**: Comprehensive usage and performance analytics

### Evaluation
- **A/B Testing**: Compare different prompts and models
- **Quality Assessment**: Automated quality scoring
- **Continuous Improvement**: Data-driven optimization

## Advanced Features

### Custom Metrics
Add custom feedback scores for domain-specific quality metrics:

```python
opik_context.update_current_trace(
    feedback_scores=[
        {"name": "resume_completeness", "value": 0.95, "reason": "All sections extracted"},
        {"name": "data_accuracy", "value": 0.88, "reason": "High confidence parsing"}
    ]
)
```

### Experiment Tracking
Use Opik's experiment features to:
- Compare different prompts
- Evaluate model performance
- Track accuracy improvements

### Production Monitoring
Set up production monitoring with:
- Real-time dashboards
- Performance alerts
- Cost optimization insights

## Troubleshooting

### Common Issues
1. **Missing Traces**: Check API key and project configuration
2. **Slow Performance**: Opik logging is asynchronous and shouldn't impact performance
3. **Dashboard Access**: Verify project name and workspace access

### Debug Mode
Enable debug logging to troubleshoot Opik integration:

```python
import logging
logging.getLogger("opik").setLevel(logging.DEBUG)
```

## Next Steps

1. **Explore Dashboard**: Familiarize yourself with the Opik interface
2. **Set Up Alerts**: Configure monitoring and alerting rules
3. **Create Experiments**: Set up evaluation experiments for continuous improvement
4. **Optimize Performance**: Use insights to optimize prompts and processing
