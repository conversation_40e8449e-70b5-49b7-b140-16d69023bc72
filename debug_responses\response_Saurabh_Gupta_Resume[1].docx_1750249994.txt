=== RAW RESPONSE ===
Length: 4078
Source: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_Resume[1].docx
Timestamp: 1750249994.1000974
=== CONTENT ===
```json
{
    "name": "<PERSON><PERSON><PERSON><PERSON>",
    "email": null,
    "phone": "+919424995834",
    "education": [
        {
            "degree": "B.E. (Civil Engineering)",
            "institution": "Technocrats Institute of Technology",
            "year": "Aug 2016 – Sept 2020"
        }
    ],
    "skills": [
        "JavaScript",
        "Typescript",
        "HTML",
        "CSS",
        "React.js",
        "Bootstrap",
        "Next.js",
        "Routes",
        "Tailwind",
        "Redux",
        "Context",
        "ASP.NET",
        "Firebase",
        "MySQL",
        "GitHub",
        "REST API",
        "Prisma",
        "NPM"
    ],
    "experience": [
        {
            "company_name": "Novaautomata Innovations Pvt Ltd.",
            "role": "Software Engineer",
            "duration": "March 2024 – Present",
            "key_responsibilities": "Full stack developer\nSQL database creation"
        },
        {
            "company_name": "Codecaffiene Technologies Pvt Ltd.",
            "role": "Software Engineer",
            "duration": "Nov 2023 – Present",
            "key_responsibilities": "Spearheaded the implementation of modern frontend libraries and tools such as Redux and Tailwind, resulting in a 30% improvement in application performance and scalability.\nCollaborated with cross-functional teams to deliver 15+ features, bug fixes, and improvements, meeting project deadlines consistently.\nUtilized Prisma ORM to optimize data systems, reducing query response time by 50%, enhancing application efficiency, and ensuring seamless scalability.\nContributed to the development of Next-Auth and JWT authentication system, enhancing user security."
        },
        {
            "company_name": "Accenture",
            "role": "Associate Software Engineer",
            "duration": "April 2022 – March 2023",
            "key_responsibilities": "Played a key role in optimizing JIT production stages for the IJCore project, reducing production time by 25%.\nDesigned and implemented user-friendly interfaces and functionalities using React and ASP .NET, resulting in a 40% increase in user satisfaction.\nConducted rigorous SQL-based data extraction and debugging, resolving 50+ critical issues and improving application stability.\nFacilitated real-time debugging sessions on live applications hosted in IIS, ensuring seamless functionality and performance."
        }
    ],
    "projects": [
        {
            "name": "Metro Mail",
            "description": "Tech Used: React.js, CSS, Redux, React Route v6, Firebase\nDesigned and developed a client email box for sending and receiving emails with a specific extension as metro.com. This resulted in improved communication efficiency.\nOptimized state management with Redux, reducing unnecessary re-renders and enhancing performance by 60%. Reduced heavy lifting tasks by 40%, ensuring a smoother user experience."
        },
        {
            "name": "Expense Tracker",
            "description": "Tech Used: React.js, CSS, Redux, React Route v6, Firebase\nDeveloped an expense management web app with authentication and storage, featuring premium features like dark/light mode switch and expense download, resulting in streamlined expense tracking for users."
        },
        {
            "name": "Paryatan",
            "description": "Tech Used: React.js, CSS, Route v6, JavaScript\nEngineered and deployed a captivating travel website with a strong emphasis on showcasing diverse destinations across India, ensuring an engaging user experience."
        }
    ],
    "certifications": [
        "Python for Beginners - Newton School (2024)",
        "Gate Civil Engineering qualifier in 2020 and 2021"
    ],
    "domain_of_interest": [
        "Software Development",
        "Web Technologies"
    ],
    "languages_known": [
        "JavaScript",
        "Typescript"
    ],
    "achievements": [
        "Solved 150+ LeetCode questions"
    ],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": null,
    "personal