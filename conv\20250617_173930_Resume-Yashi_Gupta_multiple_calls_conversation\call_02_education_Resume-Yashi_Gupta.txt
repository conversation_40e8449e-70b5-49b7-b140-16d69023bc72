================================================================================
SECTION EXTRACTION CALL #2 - EDUCATION
================================================================================
Source File: Resume-Yashi Gupta.pdf
Section: education
Call Number: 2
Timestamp: 2025-06-17T17:39:33.007803
Processing Time: 1.24 seconds
Confidence Score: 0.90
Model: gemma3:4b
================================================================================

[PROMPT]
Length: 3334 characters
----------------------------------------
Look for a section in the resume with headings like "EDUCATION", "ACADEMIC BACKGROUND", "QUALIFICATIONS", or "EDUCATIONAL QUALIFICATIONS".
Extract ONLY the text that appears directly under that specific section heading. Do not include information from other sections.
Return exactly what is written under that section, nothing more, nothing less. If no such section exists, return 'NOT_FOUND'.

Resume Text:
Yashi Gupta
LinkedIn Github HackerRank Leetcode
PROFESSIONAL SUMMARY
Full stack developer with a problem-solving mindset and strong skills in frontend, backend and DSA, ready to enhance user
experiences in a team environment.
EDUCATION
Bachelor of Technology (Computer Science) 2023 - 2027
Newton School of Technology, Rishihood University Grade: 8.57/10.0
Intermediate (Class XII) 2021 - 2022
Huddard High School Grade: 94.26%
Matriculation (Class X) 2019 - 2020
Huddard High School Grade: 93.4%
INTERNSHIPS
Frontend Intern June 2024 - August 2024
IIT Roorkee with Social Studies Foundation Remote
Tech Stack: NextJS, Tailwind CSS
Description: Developed in collaboration between IIT Roorkee and the Social Studies Foundation, this project curates and
displays government schemes, job openings, and scholarships for the SC/ST community.
Contributions: Designed lter tabs with state-wise and department-wise options, integrated APIs for categorized schemes
(scholarships, job openings, general schemes), and built responsive components like detailed scheme cards and homepage
elements for improved user experience.
PROJECTS
Expedition- Backend Python , ( Github ) ( Demo ) December 2024
Tech Stack: Python
Description: Backend in Python for a ticket booking system, Expedition.
Features: Comprehensive CRUD APIs for managing data, secure user authentication, and ecient data handling for
smooth ticket booking and selling system.
iPhone 15 Pro Website , ( Github ) ( Demo ) December 2024
Tech Stack: ReactJS, ThreeJS, GSAP
Description: iPhone 15 Pro website replica with exceptional design and functionality.
Features: Developed a precise iPhone 15 Pro website replica featuring an advanced video carousel and enhanced user
experience.
FashMore-ECommerce-Project , ( Github ) ( Demo ) September 2024
Tech Stack: React, Firebase
Description: Developed a modern e-commerce platform delivering the latest in fashion trends.
Features: Seamless browsing with organized categories, secure authentication, advanced search, ltering, and ecient cart
management.
SKILLS
Computer Languages: Java, JavaScript, CSS, HTML, TypeScript, Python
Software Packages: React, MySQL, Express JS, NodeJS, Prisma ORM, Tailwind, Hadoop HDFS
Soft Skills: Presentation Skills, Teamwork, Time management
EXTRA-CURRICULAR ACTIVITIES
Participated in a hackathon organized by Google Cloud to develop an AI-powered chatbot website.
Participated in HackCBS, the biggest student-led hackathon.
Moderator at Coding Club, Newton School of Technology.


CRITICAL INSTRUCTIONS:
- Be LITERAL and EXACT in your extraction
- Copy the content exactly as it appears under the section heading
- Do NOT include content from other sections even if it seems related
- Do NOT add any explanations or interpretations
- If the specific section heading is not found, return exactly 'NOT_FOUND'
- Maintain the original formatting and structure of the content
----------------------------------------

[RESPONSE]
Length: 120 characters
Confidence: 0.90
----------------------------------------
Bachelor of Technology (Computer Science) 2023 - 2027
Newton School of Technology, Rishihood University Grade: 8.57/10.0
----------------------------------------

================================================================================
