=== RAW RESPONSE ===
Length: 3940
Source: Resume-<PERSON><PERSON>.pdf
Timestamp: 1750248207.4279144
=== CONTENT ===
```json
{
    "name": "<PERSON><PERSON>",
    "email": null,
    "phone": null,
    "education": [
        {
            "degree": "Bachelor of Technology (Artiﬁcial intelligence)",
            "institution": "Newton school of Technology, Rishihood University",
            "year": "2023 - 2027"
        },
        {
            "degree": "Intermediate (Class XII)",
            "institution": "DELHI PUBLIC SCHOOL, HISAR",
            "year": "2021 - 2022"
        },
        {
            "degree": "Matriculation (Class X)",
            "institution": "DELHI PUBLIC SCHOOL, HISAR",
            "year": "2019 - 2020"
        }
    ],
    "skills": [
        "C++",
        "Python",
        "JavaScript",
        "CSS",
        "HTML",
        "Java",
        "React",
        "Express JS",
        "Prisma ORM",
        "NodeJS",
        "MongoDB",
        "React",
        "CSS",
        "JavaScript",
        "Tailwind",
        "HTML",
        "Bootstrap",
        "Data Structure"
    ],
    "experience": [],
    "projects": [
        {
            "name": "Theta: The Learning App",
            "description": "Theta is a cutting-edge E-learning platform designed to provide personalized, engaging educational content. \n\nTailored for diverse learners, Theta makes learning accessible and interactive anytime, anywhere.\n\nFeatures:\n\nAs an admin user can perform CRUD operations to create Courses, Lectures and manage costs of it.\n\nAs a buyer user can buy course to watch lectures and learn.\n\nAs a superadmin user can manage roles and permissions of admin and user.\n\nRazorpay APIs also have been integrated.\n\nUsed react-router-dom for routing and pagination for interactive and immersive user experience and can create and log\ninto their respective accounts securely."
        },
        {
            "name": "The Souled Store Website Clone",
            "description": "Developed a replica of The Souled Store using React, demonstrating front-end proﬁciency and ensuring a seamless user experience across various devices.\n\nFeatures:\n\nImplemented modular React components for eﬃcient UI management.\n\nManaged application state eﬀectively across components.\n\nSet up routing for seamless navigation between pages.\n\nUtilized Context API for global state management.\n\nEnsured a fully responsive, mobile-ﬁrst design."
        },
        {
            "name": "Art Gallery Website",
            "description": "Developed a dynamic online Art Gallery platform connecting art enthusiasts, collectors, and creators worldwide, oﬀering an engaging and user-friendly experience.\n\nFeatures:\n\nCurated a diverse collection of artworks from both established and emerging artists across various mediums.\n\nEnabled users to discover, buy, and sell art in a seamless marketplace.\n\nFostered a vibrant community for expanding collections and showcasing unique creations."
        }
    ],
    "certifications": [
        "Python for Beginners - Newton School (2024)"
    ],
    "domain_of_interest": [],
    "languages_known": [],
    "achievements": [
        "Contributed to shaping an innovative and impactful event as a core team member of Neutron Fest, India's pioneering new-age AI Techno Cultural Fest.",
        "Serving as the Secretary of Google Developer Group at Rishihood University.",
        "Held the position of Chief of the Maratha Clan within the Rishihood Community, managing responsibilities with honor and leadership."
    ],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": "Enthusiastic and innovative Frontend Developer with a passion for clean and eﬃcient code, eager to contribute to dynamic teams and make a meaningful impact in the ﬁeld of web development.",
    "personal_projects": [],
    "social_media": [
        "Github",
        "HackerRank",
        "CodeChef",
        "Codeforces",
        "Leetcode",
        "Personal Portfolio"
    ]
}
```