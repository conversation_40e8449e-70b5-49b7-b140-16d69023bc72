{"event": "session_start", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "timestamp": "2025-06-19T22:14:27.354798", "message": "New API session started"}
{"event": "request_start", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "0d2e42e1-f7db-4cdd-9674-116ec0a4d7ed", "endpoint": "/", "timestamp": "2025-06-19T22:14:32.413577", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "0d2e42e1-f7db-4cdd-9674-116ec0a4d7ed", "endpoint": "/", "timestamp": "2025-06-19T22:14:32.415577", "total_time_seconds": 0.0020008087158203125, "status_code": 200, "message": "Request completed in 0.0020s with status 200"}
{"event": "request_start", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "e04f451a-5ae9-4ace-a68e-4099e71a48ae", "endpoint": "/favicon.ico", "timestamp": "2025-06-19T22:14:32.843851", "message": "Request started for endpoint: /favicon.ico"}
{"event": "request_complete", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "e04f451a-5ae9-4ace-a68e-4099e71a48ae", "endpoint": "/favicon.ico", "timestamp": "2025-06-19T22:14:32.844851", "total_time_seconds": 0.0009996891021728516, "status_code": 404, "message": "Request completed in 0.0010s with status 404"}
{"event": "request_start", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "35650f32-7f6d-4113-8288-6d8531378b62", "endpoint": "/docs", "timestamp": "2025-06-19T22:14:36.400797", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "35650f32-7f6d-4113-8288-6d8531378b62", "endpoint": "/docs", "timestamp": "2025-06-19T22:14:36.401796", "total_time_seconds": 0.0009992122650146484, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "f50d98c9-113e-458a-93ad-e3f69cd56a58", "endpoint": "/openapi.json", "timestamp": "2025-06-19T22:14:36.513796", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "f50d98c9-113e-458a-93ad-e3f69cd56a58", "endpoint": "/openapi.json", "timestamp": "2025-06-19T22:14:36.542796", "total_time_seconds": 0.029000520706176758, "status_code": 200, "message": "Request completed in 0.0290s with status 200"}
{"event": "request_start", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "44aaad6b-a245-4c9a-87e2-ad4ffc061d7c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-19T22:14:47.730946", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "44aaad6b-a245-4c9a-87e2-ad4ffc061d7c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-19T22:14:47.799989", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "44aaad6b-a245-4c9a-87e2-ad4ffc061d7c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-19T22:14:47.813982", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "44aaad6b-a245-4c9a-87e2-ad4ffc061d7c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-19T22:14:47.819985", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "44aaad6b-a245-4c9a-87e2-ad4ffc061d7c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-19T22:14:47.824984", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "44aaad6b-a245-4c9a-87e2-ad4ffc061d7c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-19T22:14:47.827982", "file_processing_time": 0.060038089752197266, "message": "Custom metric: file_processing_time=0.060038089752197266"}
{"event": "request_complete", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "44aaad6b-a245-4c9a-87e2-ad4ffc061d7c", "endpoint": "/hybrid_resume", "timestamp": "2025-06-19T22:14:50.029565", "total_time_seconds": 2.298619508743286, "status_code": 200, "message": "Request completed in 2.2986s with status 200"}
{"event": "request_start", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "fab5ece9-0243-4598-9658-a0432cab17ce", "endpoint": "/hybrid_resume", "timestamp": "2025-06-19T22:14:57.326138", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "fab5ece9-0243-4598-9658-a0432cab17ce", "endpoint": "/hybrid_resume", "timestamp": "2025-06-19T22:14:57.349139", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "fab5ece9-0243-4598-9658-a0432cab17ce", "endpoint": "/hybrid_resume", "timestamp": "2025-06-19T22:14:57.349139", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "fab5ece9-0243-4598-9658-a0432cab17ce", "endpoint": "/hybrid_resume", "timestamp": "2025-06-19T22:14:57.349139", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "fab5ece9-0243-4598-9658-a0432cab17ce", "endpoint": "/hybrid_resume", "timestamp": "2025-06-19T22:14:57.349760", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "fab5ece9-0243-4598-9658-a0432cab17ce", "endpoint": "/hybrid_resume", "timestamp": "2025-06-19T22:14:57.349760", "file_processing_time": 0.01899862289428711, "message": "Custom metric: file_processing_time=0.01899862289428711"}
{"event": "request_complete", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "fab5ece9-0243-4598-9658-a0432cab17ce", "endpoint": "/hybrid_resume", "timestamp": "2025-06-19T22:14:59.404409", "total_time_seconds": 2.079267740249634, "status_code": 200, "message": "Request completed in 2.0793s with status 200"}
{"event": "request_start", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "9c7a54de-6ff8-4163-a4bb-93c672f1cad1", "endpoint": "/hybrid_resume", "timestamp": "2025-06-19T22:16:09.682519", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "9c7a54de-6ff8-4163-a4bb-93c672f1cad1", "endpoint": "/hybrid_resume", "timestamp": "2025-06-19T22:16:09.704725", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "9c7a54de-6ff8-4163-a4bb-93c672f1cad1", "endpoint": "/hybrid_resume", "timestamp": "2025-06-19T22:16:09.704725", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "9c7a54de-6ff8-4163-a4bb-93c672f1cad1", "endpoint": "/hybrid_resume", "timestamp": "2025-06-19T22:16:09.704725", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "9c7a54de-6ff8-4163-a4bb-93c672f1cad1", "endpoint": "/hybrid_resume", "timestamp": "2025-06-19T22:16:09.704725", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "9c7a54de-6ff8-4163-a4bb-93c672f1cad1", "endpoint": "/hybrid_resume", "timestamp": "2025-06-19T22:16:09.704725", "file_processing_time": 0.01787400245666504, "message": "Custom metric: file_processing_time=0.01787400245666504"}
{"event": "request_complete", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "9c7a54de-6ff8-4163-a4bb-93c672f1cad1", "endpoint": "/hybrid_resume", "timestamp": "2025-06-19T22:16:28.252659", "total_time_seconds": 18.570140600204468, "status_code": 200, "message": "Request completed in 18.5701s with status 200"}
{"event": "request_start", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "14cf872b-af90-4665-9aaf-2a2bc5c7c4af", "endpoint": "/hybrid_resume", "timestamp": "2025-06-19T22:17:26.726149", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "14cf872b-af90-4665-9aaf-2a2bc5c7c4af", "endpoint": "/hybrid_resume", "timestamp": "2025-06-19T22:17:26.770138", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "14cf872b-af90-4665-9aaf-2a2bc5c7c4af", "endpoint": "/hybrid_resume", "timestamp": "2025-06-19T22:17:26.771260", "file_size_bytes": 84438, "message": "Custom metric: file_size_bytes=84438"}
{"event": "custom_metric", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "14cf872b-af90-4665-9aaf-2a2bc5c7c4af", "endpoint": "/hybrid_resume", "timestamp": "2025-06-19T22:17:26.771260", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "14cf872b-af90-4665-9aaf-2a2bc5c7c4af", "endpoint": "/hybrid_resume", "timestamp": "2025-06-19T22:17:26.771260", "extracted_text_length": 2855, "message": "Custom metric: extracted_text_length=2855"}
{"event": "custom_metric", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "14cf872b-af90-4665-9aaf-2a2bc5c7c4af", "endpoint": "/hybrid_resume", "timestamp": "2025-06-19T22:17:26.771260", "file_processing_time": 0.04068183898925781, "message": "Custom metric: file_processing_time=0.04068183898925781"}
{"event": "request_complete", "session_id": "ef4acd01-51ea-4b1c-ab90-81ceaae73e9c", "request_id": "14cf872b-af90-4665-9aaf-2a2bc5c7c4af", "endpoint": "/hybrid_resume", "timestamp": "2025-06-19T22:17:47.379429", "total_time_seconds": 20.65328025817871, "status_code": 200, "message": "Request completed in 20.6533s with status 200"}
