================================================================================
SECTION EXTRACTION CALL #2 - EDUCATION
================================================================================
Source File: Balpreet_SrFullstack Java.docx
Section: education
Call Number: 2
Timestamp: 2025-06-17T17:38:30.665500
Processing Time: 2.03 seconds
Confidence Score: 0.00
Model: gemma3:4b
================================================================================

[PROMPT]
Length: 24702 characters
----------------------------------------
Look for a section in the resume with headings like "EDUCATION", "ACADEMIC BACKGROUND", "QUALIFICATIONS", or "EDUCATIONAL QUALIFICATIONS".
Extract ONLY the text that appears directly under that specific section heading. Do not include information from other sections.
Return exactly what is written under that section, nothing more, nothing less. If no such section exists, return 'NOT_FOUND'.

Resume Text:
Balpreet Singh
Sr. Full Stack Java Developer
Email: <EMAIL>
Phone: +1(469)205-8867

PROFESSIONAL SUMMARY:
Experienced Senior Java professional with 10+ Years of IT experience in various roles and domains with OOAD, development, testing, maintenance of distributed and Client/Server Java applications using Java/J2EE. 
Experience in large scale SDLC including Requirements Analysis, Project Planning, System and Database Design, Object Oriented Analysis and Design, utilizing Agile-Scrum, Waterfall, TDD, BDD methodology.
Expertise with developing UI using Angular 4/7/8/10/16, React JS, jQuery, Ext.JS, Node.JS, Graph QL and Express.JS. 
Experience with upgrading SOAP WS application into Microservices application and used Spring Boot implementation. Good experience in J2EE design patterns like Session Façade, Business Object, Service Locator, Data Transfer Object, Adapter, Mediator, observer, and Factory. 
Experience with Multithreading, Executor framework, Data Structures, Collections and Exception Handling concepts. Well versed with core Java concepts like serialization, Java beans. 
Experience with Java 8 features- Lambdas, Streams, Pipelines, Filters, Optional and Concurrency Packages.
Experience with various Spring modules such as Spring MVC, Spring Batch, Spring IOC, Spring Security, Spring AOP, Spring Dependency Integration, Spring Core, Spring Auto Wiring, Spring Transaction, Spring Cloud using Spring framework with back end ORM tool.
Experience with Microservices Architecture applications using Spring Boot, Spring Cloud Config, Restful Web Services and implemented Microservices design patterns- Hystrix, Circuit Breaker. 
Extensive experience in J2EE technologies such as JDBC, JSP, Servlets, JSF, EJB, Struts, Spring, Hibernate.
Experience in implementing and deploying SOAP API Web Services JAX-WS, WSDL, UDDI, and Restful Services JAX-RS to develop dynamic Server-Side web contents.
Experience in SAX, DOM. Developed UI using HTML, CSS, JavaScript, JSON, ReactJS, CSS3, JSP tags to simplify the complexities.
Experience with deploying the applications in AWS as EC2 instances, snapshots for data that had to be stored in AWS S3. Services: IAM, EC2, S3, EBS, VPC, ELB, RDS, Dynamo DB, Auto Scaling, Security Groups, Redshift, Amazon AWS IAM Services in Users, Groups, Policies, Roles, AWS Access Keys and MFA.
Created AWS Security Groups for deploying and configuring AWS EC2 instances. 
Good knowledge of designing, deploying, and operating highly available, scalable and fault tolerant systems using Azure.
Experience in SPOCK is a testing framework for Java and Groovy applications that is inspired by BDD principles.
Hands on experience with build tools like Ant, Maven, Gradle and Logging tools like Log4J and Logger.
Experience on DEVOPS tools, CI/CD and AWS Cloud Architecture in database modelling and developing using No SQL Database such as Mongo DB and Cassandra.
Lead the implementation of monitoring and logging solutions resulting in reduced resolution time. Good working knowledge of SQL, Stored Procedures, Joins and Triggers with RDBMS databases like Oracle, Postgres, MySQL. Good exposure to NoSQL databases like MongoDB, DynamoDB, Cassandra DB. 
Hands on experience in deploying and configuration multi-tier enterprise applications on various Application servers and Web Servers which includes Apache Tomcat 8.x/9.x, Web logic Server 11g, JBoss, WebSphere.
Good hands on developing J2EE applications using IDEs like Eclipse, IntelliJ, STS, WebStorm. 
Hands on experience on integrating Spring with Hibernate, implemented Transaction, mappings, Cache.
Proficient in deploying applications using Maven, Jenkins, SonarQube, Bamboo, Cucumber, Docker tools.
Experience with controlling/tracking systems Subversion, Bit bucket, GIT.
Knowledge in AWS CI/CD pipeline like Code Commit, Code Build and Code Deploy. 
Solid experience in Healthcare, Financial, Insurance and Retail domains. 
Extensive experience in testing applications using Junit, Mockito, TestNG, Selenium, Protractor, Cucumber.
TECHNICAL SKILLS: 
Languages: C, C++, Java 17/11/9/8/7/5, Python, PL/SQL
J2EE Technologies:	JSP, Servlets, Struts 1&2, Spring4/5, Spring MVC, Spring Boot, EJB, Hibernate 3.x, JSTL, JMS, Log4j, JDBC, Java Beans, JAX-RS, JAX-WS 
Web Services:	SOAP, REST, REST API, Apache CXF
Application/Web Servers: Apache Tomcat 9.x, J-Boss, Web Logic, Web Sphere, Apache Kafka
Methodologies: Agile, Scrum, J2EE Design Patterns, Waterfalls, Safe Agile.
Built Tools: Jenkins, Docker, Kubernetes, CI/CD, Azure, Google cloud, PCF
Web Technologies:  HTML5, DHTML, XHTML, AJAX, XML, J son, J query, JavaScript ES6, CSS3, Angular JS, Angular 6/7/8/10/12/16, Node JS, React JS, JSF, JSTL, DOM, JAXB and JAXP.
IDE Tools: Eclipse, MyEclipse, STS, IntelliJ, PyCharm, WebStorm.
Versioning Tools: SVN, Git, GitHub, TFS, Bitbucket.
Development Tools: Ant, Maven, Junit
ORM, Design Patterns: Hibernate, Creational, Structural, Behavioral
AWS: EC2, RDS, S3, ELB, EBS
Databases/API: Oracle, SQL, PL SQL, No SQL, MONGODB, MySQL, MS Access, MS SQL Server, Triggers, View, Criteria and Toad.
Data Modelling: Microsoft Visio, Rational Rose and UML Design
Tracking tools: Jira, Rally, Firebug, Firebug Lite, Bugzilla.
Operating Systems: UNIX/Linux, Windows XP/Vista/7/8/11
PROFESSIONAL EXPERIENCE: 
Client:  AIM Specialty Health, Chicago, IL                                                   	                Oct 2023 – Till Date  
Role: Sr. Full Stack Java Developer
Responsibilities:
Followed Agile methodology and involved in daily SCRUM meetings, sprint planning, showcases and retrospective and used Rally as a tracking tool for the sprint.
Developed cross and multi browser compatible web pages by using HTML5, CSS4 and Bootstrap.
Implemented Single page web applications using Restful web services using AJAX and Angular 16.
Used Node.js for non-blocking design operation for server-side and Angular16 for developing UI components.
Demonstrated expertise in utilizing Angular 16 modules, services, templates, directives, dependency, lazy loading, and differential loading to create responsive SPA.
Improved overall performance via Multithreading, collections, synchronization, and Exception Handling.
Used Java 17 features like Predicates, Lambdas and Stream API for bulk data operations on Collections.
Created, produced, consumed REST web services for server requests and for third party system interaction.
Worked on Microservices based business components and RESTful service endpoints using Spring boot.
Developed server-side application to interact with database using Spring Boot and Hibernate.
Used Spring Framework including Spring IOC, Spring AOP, Spring ORM, Spring Batch and implemented the authentication, authorization, and access-control features by making use of Spring Security feature.
Developed RESTful web service using the Spring Reactive Handler, Router and Web Client components to perform the business needs.
Developed UI pages using HTML5, CSS4, Angular 16. Extensively used Java 11 APIs Streams, Lambdas, and Filters and Involved in migrating java application from Java 8 to Java 17 and implement with hibernate.
Used application using Spring Boot Framework and security using Spring Security. Implemented Spring Boot Security with OAUTH2 and managed application Authentication and Authorization.
Experienced in   Spring boot Microservices to process the messages into the Kafka cluster setup.
Implemented Kafka producer and consumer applications on Kafka cluster setup with help of Zookeeper.
Implemented various screens for the front end using React JS and used various predefined components from Node Package Manager (NPM) and redux library.
Developed RESTful Microservices using Spring technologies - Spring Boot, Spring Security, Spring Data, used Postman and swagger to test Microservice which is a web hook service component.
Built Microservices using Spring Boot, Spring Security, JPA and deployed to AWS Cloud.
Worked with modules like MYSQL and mongoose for persistence using Node JS to interact with MYSQL.
Worked on MYSQL data model design based on application used embedded and normalized data models. 
Worked on Cucumber test cases for functional testing as part of Automation testing for Dashboard and Integrating with Jenkins with Maven to Compile & Build Microservices code and configure Build Triggers.
Expertise in searching, monitoring, analyzing and visualizing Splunk logs and Splunk enterprise deployments.
Utilized Kubernetes for the runtime environment of the CI/CD system to build, test deploy. 
Kubernetes is being used to orchestrate the deployment, scaling, and management of Docker Containers
Managed and automated the deployment of microservices using Kubernetes and Worked with OpenShift platform in managing Docker containerization and Kubernetes Clusters.
Developed CI/CD pipeline and build tasks to run the unit tests on Jenkins and maintained separate build in Gradle for the module for easy integration to the portal build and configure with Jenkins.
Extensively involved in a Docker deployment pipeline for custom application Images in the private cloud using Jenkins. Involved in resolving production defects with service now portal.
Jenkins to auto deploy client-side application based on git branches, debug server is triggered by git push while release server is based on nightly build.
Deployed and configured Git repositories with branching, forks, tagging, and notifications. Experienced and proficient deploying and administering GitHub.
Utilized MongoDB's indexing and sharing capabilities to optimize query performance and accommodate the increasing data load, resulting in improved application responsiveness.
Environment: Java 17, HTML 5, CSS4, Bootstrap, TypeScript, Angular 16, Ajax, Spring Frameworks, Spring Batch, Postman, Microservices, IntelliJ, STS, AWS, Jenkins, JavaScript, MY SQL, Splunk, Gradle, Maven, Kafka, OpenShift/Kubernetes, Reactive framework Redis, Swagger, Docker, Rally, GitHub.
Client:  Capital One - McLean, VA                                                                      	               Apr 2021 – Sep 2023  
Role: Sr. Full Stack Java Developer
Responsibilities:
Followed Agile methodology and involved in daily SCRUM meetings, sprint planning, retrospective and used JIRA as a tracking tool for the sprint.
Developed cross and multi browser compatible web pages by using HTML5, CSS4 and Bootstrap.
Used Node.js for non-blocking design operation for server-side and Angular10 for developing UI components.
Developed UI pages using HTML, XHTML, jQuery, HTML5, AJAX, CSS4, Angular 10. Extensively used Java8 APIs Streams, Lambdas, and Filters. 
Implemented Native application using Angular10 and scaled to fit for desktop & mobile, creating components.
Used server-side application to interact with database using Spring Boot and Hibernate.
Used Java 8 features like Predicates, Functional Interfaces and Lambda Expressions, Method References and Stream API for bulk data operations on Collections and used Java features.
Improved overall performance via Multithreading, collections, synchronization, and Exception Handling.
Lead the design and development of microservice business components, employing Java, Spring Boot, Spring Security, Spring Batch, and Spring Data JPA, while implementing REST web services based on RESTful APIs.
Spring Batch framework was implemented to automate and streamline batch processing activities. This improved system performance and data processing efficiency by assuring effective handling of huge amounts of data, job scheduling, and robust error handling.
Utilized Spring and Hibernate frameworks to develop the DAO layer of the application, implemented business logic and generating reports by leveraging HQL and Hibernate Criteria, ensuring efficient data access. 
Worked on developing Restful endpoints to cache application specific data in in-memory data clusters like REDIS and exposed them with Restful endpoints, Used Microservices architecture, with Spring Boot based services interacting through a combination of REST and Apache Kafka endpoints.
Created, produced, and consumed REST web services to server requests and for third party system interaction. Implemented Spring MVC RESTful services and consumed via HTTP services from Angular9.
Developed RESTful Microservices using spring technologies - Spring Boot, Spring Security, Spring Data, used Postman to test Microservice which is a web hook service component.
Used Hibernate framework with Spring Framework for data persistence and transaction management.
Worked with modules like PostgreSQL for database persistence using Node JS to interact with PostgreSQL.
Worked on PostgreSQL data model design based on application, used embedded, normalized data models.
Developed Hibernate to migrate data from PostgreSQL Created PostgreSQL driver for multi-site replication. 
Implemented and maintained the monitoring and alerting of corporate storage using AWS Cloud watch.
Managed infrastructure provisioning ​application deployment and monitoring worked with event-driven and scheduled AWS Lambda functions to trigger various AWS resources. 
 business applications to Amazon cloud services-based environment and deployed applications into the AWS cloud using Amazon EC2, IAM, AWS S3, ECS, EKS, VPC, RDS, Dynamo DB and Configuring and versioning the AWS S3 buckets and creating the lifecycle policies to store and archive data to Amazon Glacier.
Developed Cucumber test cases for functional testing as part of Automation testing for frontend. 
Developed CI/CD pipeline and build tasks to run the unit tests on Jenkins and maintained separate build in Maven for the module for easy integration to the portal build and configure with Jenkins.
 involved in a Docker pipeline for custom application Images in the private cloud using Bogi files.
Jenkins to auto deploy client-side application based on git branches, debug server is triggered by git push while release server is based on nightly build.
Deployed and configured Git repositories with branching, forks, tagging, and notifications. Experienced and proficient deploying and administering GitHub.
Used MongoDB queries in creating and managing indexes to speed up data access and reduce query response.
involved in integrating MongoDB with other systems or migrating data from a different database to MongoDB.
Environment: Java 8, HTML 5, CSS4, Bootstrap, JavaScript, Angular 10, Ajax, Spring Frameworks, Postman, Microservices, AWS Services, LOG 4j 2, IntelliJ IDE, PostgreSQL, Bogi files, SQL, Maven, Docker, Jira, GitHub.
Client: Walmart, Bentonville, AR						         Nov 2019 – March 2021
Role: Full Stack Java Developer	
Responsibilities:
Analyzed business requirements. Participated in all phases of SDLC Prioritized, estimated, and actively implemented user stories every sprint in the Agile Scrum environment. 
Designed Frontend within object-oriented JavaScript Framework like React.JS. Created forms to collect and validate data from the user in HTML5 and React.JS.
Maintained existing UI Applications and upgraded them using CSS4, jQuery, AJAX, GWT, JavaScript, React JS, Backbone.js, JSON and HTML5.
Used Java 8 features like Parallel Streams, Lambdas, Functional Interfaces and Filters for fast performance.
Involved in migration of java 1.8 to java 8 like configuration of project, changes in code, monitoring.
Extensively worked on Collections Framework, and Exception Handling, I/O System, Multi-Threading, Generics, and other new features in Java8 – Lambdas, Streams, Functional, Concurrency packages for multi-threading and optional interfaces
Implemented the REST API's and data transformers using the JAVA connector which involves using Java 8 features Lambda and Streams. Maintained Interface compatibility and concurrency in the project using Java 1.8 new features like Lambda expressions, static methods, and Concurrency API.
Developed the application with various Spring Framework modules like Spring IOC, Spring AOP, Spring Boot, Spring Security, and Spring Batch, Spring Eureka, Spring Netflix, Spring Zulu.
Implemented Spring Quartz Batch processing to process statements for Insurance claims and benefits.
Developed Spring Boot REST Microservices, Docker Containers and deployed into AWS EC2 instances.
Integrated Spring Boot Microservices with Apache Kafka for Asynchronous messaging.
Implemented Persistence layer using Spring Boot Data JPA and performed CRUD operations against MYSQL DB. Worked on Swagger UI to interact with API’s. Designed and documented API’s using Swagger.
Implemented React JS integration with backed applications and made REST calls via Redux Saga approach.
Implemented the Project Structure based on Spring MVC pattern using Spring Boot and Spring Beans. Designed and developed Rewards API using Spring Boot. Developed RESTful services for transmission of data in JSON format.
Used JPA Annotations concept to retrieve data from the database and integrate with Spring Boot to interact with back end MYSQL Developed Server-side components with coordination of JPA and spring.
Used Kafka technology to drop messages from one Microservice into one Kafka topic and receive it from another topic. In between two topics, the message is transferred, processed through another Microservice.
Developed Spring Kafka message listeners to create and consume messages across modules from app. 
Involved in designing, building, and deploying a multitude of applications utilizing almost all of AWS (Including EC2, S3, SQS and EMR), focusing on high-availability, fault tolerance, and auto-scaling.
Developed Java API to connect with AWS S3 Services for storing and retrieving data from S3 buckets.
Worked on Design, investigation and implementation of public facing websites on Amazon Web Services and Configured Elastic Load Balancers with EC2 Auto scaling groups.
Developed Automation framework and Worked on Test automation framework using Selenium web driver. 
Worked with Cucumber, Selenium Web Driver, Commands and X-path, developed grunt tasks to run unit tests on Jenkins. Used JIRA to assign, track, report and audit the issues in the application.
Worked with testing frameworks such as Mockito. used Maven tool to build and deploy J2EE applications.
Used Bitbucket as Repository version control system for source code and project documents, bug fixing.
Used JIRA to track the progress of the project and wrote test cases, Used as Mockito a testing framework. 
Installation and configuration of MongoDB. Understanding of CRUD operations and queries to achieve it. Creating Database/Collection/Document and Index creation and managing it.
Environment: Java 8, J2EE, HTML 5, JavaScript, JSON, jQuery, AJAX, CSS 3, Spring Frameworks, Microservices, DAO, Web Services, Kafka, AWS Services, WebLogic, MySQL, JPA, MongoDB, Bitbucket, Jenkins, Maven, JIRA, Mockito.
Client: Liberty Mutual Insurance - Boston, MA					Feb 2018– Oct 2019
Role: Sr. Java Developer
Responsibilities:
Involved in Agile Methodology process includes bi-weekly sprint and daily scrum to discuss work progress.
Developed all the UI pages using HTML 5, DHTML, XHTML, DOM, CSS3, Bootstrap, JavaScript and JQUERY.
Used the functionalities to write code in HTML5, CSS3, Angular 6, JavaScript, jQuery, Ajax, JSON, NodeJS and Bootstrap with Oracle database as the backend.
Implemented different validation controls on the webpages using Angular 6 and developed a cross-platform JavaScript based solution using Angular 6. Used Angular 6 latest features with Typescript, Ng Upgrade, performance, mobile development.
Implemented spring boot micro services to process the messages into the kafka cluster setup. Used Spring kafka API calls to process the messages on Kafka Cluster setup. Build Google cloud pipeline using spring boot.
Developed mapping between google cloud Pub Sub and Kafka messaging queue. Transferring the files using WinSCP server. Creating data flow jobs for google cloud Pub Sub to Big Query using Apache beans.
Design unit test cases for google cloud applications and Kafka cluster. Worked on Integration of two frameworks in single application.
Fixing the bug issues in maven environment, during the development of application. Testing the application to send the data from Kafka messaging queue to publish messages in google cloud Pub Sub.
Worked on server side by using Spring Boot, Spring Rest Service, Spring Security and Spring Batch, integrated the Spring Boot API with Angular 6 to store logs and generated deployment services.
Used Microservices, with Spring Boot based services a combination of REST Apache Kafka endpoints.
Worked with Spring IOC for implementing the future of DI across layers and implemented AOP for logging and auditing. 
Developed RESTful Microservices using spring technologies - Spring Boot, Spring Security, Spring Data, used Postman to test Micro service which is a web hook service component.
Used spring framework to achieve loose coupling between the layers thus moving towards Service Oriented Architecture exposed through SOAP.
Used Maven build tool to build and deploy the application JBOSS Application Servers. 
Involved in a Docker deployment pipeline for custom application Images in the private cloud using Jenkins.
Deployed and configured Git repositories with branching, forks, tagging, and notifications. Experienced and proficient deploying and administering GitHub.
Environment: Java/J2EE, HTML 5, DHTML, XHTML, DOM, CSS3, Bootstrap, JavaScript, Angular 6, Ajax, CSS 3, Spring Frameworks, Postman, Microservices, Web Services, JBOSS, Eclipse, PostgreSQL, Cassandras, TOAD, SQL, Maven, Docker, Firebug, TestNG, GitHub.
Client:	DBS Bank, Mumbai, India						             Oct 2013 – Jun 2016
Role:  Java/J2EE Developer
Responsibilities:
Involved in analysis, design, and development phases of Software development Life Cycle (SDLC).
Implemented presentation tier using the Spring CORE, Spring MVC. Implemented Batch Jobs for Patient Export / Import functionality using Spring Batch.
Developed application service components and configured beans using Spring IOC, creation of Hibernate mapping files and generation of database schema.
Implemented Spring Beans using IOC and transaction management features to handle the business logic.
Wrote persistent Hibernate mapping classes using annotations approach.
Developed user interface using JSP, HTML, CSS, JavaScript to simplify the complexities of the application and worked on XML parser and XML beans as well.
Interfaced using Web services, SOAP and RESTful for producing and consuming the data information. 
Synthesized database information to be rendered with React components compos from Bootstrap.
Developed front end web pages by using HTML, Node JS, Angular 4, CSS3 and JSON, JDBC, Web Services.
Used AngularJS as framework to create Single Page Applications which can bind data to specific views.
Created Angular 4 directives services and controllers for complete ground up development migration of existing jQuery applications.
Upgraded existing Angular application to Angular 4 and maintained internal application developed using Angular JS.
Implemented different validation controls on the webpages using Angular 4 and developing a cross-platform JavaScript based solution using Angular 4.
Selected the appropriate AWS service based on data, or security requirements. Connect Cluster in SQL Workbench using Amazon Redshift Cluster.
Environment: Java, Java EE, JSP, Spring MVC, JDBC Template, SOAP, HTML, XHTML, Node JS, Angular 4, CSS3, JavaScript, Spring Core, ECMAScript 6, jQuery, WSDL, XML, XSL/XSLT, ORM, AWS Services, Hibernate, ANT, AJAX, Web Services, JSON, XML, PostgreSQL, SQL, PL/SQL, WebSphere, Eclipse, JUnit.
Education:
Master in Information Technology, New England College, 2017, USA
Bachelor in Electronics and Telecommunication Engineering, Mumbai University 2013, India

CRITICAL INSTRUCTIONS:
- Be LITERAL and EXACT in your extraction
- Copy the content exactly as it appears under the section heading
- Do NOT include content from other sections even if it seems related
- Do NOT add any explanations or interpretations
- If the specific section heading is not found, return exactly 'NOT_FOUND'
- Maintain the original formatting and structure of the content
----------------------------------------

[RESPONSE]
Length: 9 characters
Confidence: 0.00
----------------------------------------
NOT_FOUND
----------------------------------------

================================================================================
