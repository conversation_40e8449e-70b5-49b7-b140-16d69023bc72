{"event": "session_start", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "timestamp": "2025-06-25T16:33:28.353271", "message": "New API session started"}
{"event": "request_start", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "3075b3c2-7d5e-4545-b608-d185b815eede", "endpoint": "/", "timestamp": "2025-06-25T16:33:48.146335", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "3075b3c2-7d5e-4545-b608-d185b815eede", "endpoint": "/", "timestamp": "2025-06-25T16:33:48.147397", "total_time_seconds": 0.0010619163513183594, "status_code": 200, "message": "Request completed in 0.0011s with status 200"}
{"event": "request_start", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "607324de-7f46-4f2b-8d67-0201b3a4205f", "endpoint": "/", "timestamp": "2025-06-25T16:33:57.233920", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "607324de-7f46-4f2b-8d67-0201b3a4205f", "endpoint": "/", "timestamp": "2025-06-25T16:33:57.234924", "total_time_seconds": 0.0010037422180175781, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "59ee8056-0285-4b7f-bd92-138c14fdc932", "endpoint": "/resume", "timestamp": "2025-06-25T16:33:59.287473", "message": "Request started for endpoint: /resume"}
{"event": "custom_metric", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "59ee8056-0285-4b7f-bd92-138c14fdc932", "endpoint": "resume", "timestamp": "2025-06-25T16:33:59.289473", "message": "Custom metric: endpoint=resume"}
{"event": "custom_metric", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "59ee8056-0285-4b7f-bd92-138c14fdc932", "endpoint": "/resume", "timestamp": "2025-06-25T16:33:59.289473", "file_name": "Resume-Raman Luhach.pdf", "message": "Custom metric: file_name=Resume-Raman Luhach.pdf"}
{"event": "custom_metric", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "59ee8056-0285-4b7f-bd92-138c14fdc932", "endpoint": "/resume", "timestamp": "2025-06-25T16:33:59.289473", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "59ee8056-0285-4b7f-bd92-138c14fdc932", "endpoint": "/resume", "timestamp": "2025-06-25T16:33:59.290475", "file_size": 73845, "message": "Custom metric: file_size=73845"}
{"event": "custom_metric", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "59ee8056-0285-4b7f-bd92-138c14fdc932", "endpoint": "/resume", "timestamp": "2025-06-25T16:33:59.323680", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "59ee8056-0285-4b7f-bd92-138c14fdc932", "endpoint": "/resume", "timestamp": "2025-06-25T16:33:59.323680", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "59ee8056-0285-4b7f-bd92-138c14fdc932", "endpoint": "/resume", "timestamp": "2025-06-25T16:33:59.323680", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "59ee8056-0285-4b7f-bd92-138c14fdc932", "endpoint": "/resume", "timestamp": "2025-06-25T16:33:59.323680", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "59ee8056-0285-4b7f-bd92-138c14fdc932", "endpoint": "/resume", "timestamp": "2025-06-25T16:33:59.323680", "file_processing_time": 0.03220200538635254, "message": "Custom metric: file_processing_time=0.03220200538635254"}
{"event": "custom_metric", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "59ee8056-0285-4b7f-bd92-138c14fdc932", "endpoint": "/resume", "timestamp": "2025-06-25T16:33:59.323680", "text_extraction_time": 0.03220200538635254, "message": "Custom metric: text_extraction_time=0.03220200538635254"}
{"event": "custom_metric", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "59ee8056-0285-4b7f-bd92-138c14fdc932", "endpoint": "/resume", "timestamp": "2025-06-25T16:33:59.323680", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "59ee8056-0285-4b7f-bd92-138c14fdc932", "endpoint": "/resume", "timestamp": "2025-06-25T16:34:30.504975", "parsing_time": 31.17981481552124, "message": "Custom metric: parsing_time=31.17981481552124"}
{"event": "custom_metric", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "59ee8056-0285-4b7f-bd92-138c14fdc932", "endpoint": "/resume", "timestamp": "2025-06-25T16:34:30.505974", "confidence_score": 0.58, "message": "Custom metric: confidence_score=0.58"}
{"event": "custom_metric", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "59ee8056-0285-4b7f-bd92-138c14fdc932", "endpoint": "/resume", "timestamp": "2025-06-25T16:34:30.505974", "fields_extracted": 18, "message": "Custom metric: fields_extracted=18"}
{"event": "custom_metric", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "59ee8056-0285-4b7f-bd92-138c14fdc932", "endpoint": "/resume", "timestamp": "2025-06-25T16:34:30.505974", "skills_count": 12, "message": "Custom metric: skills_count=12"}
{"event": "custom_metric", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "59ee8056-0285-4b7f-bd92-138c14fdc932", "endpoint": "/resume", "timestamp": "2025-06-25T16:34:30.505974", "education_count": 3, "message": "Custom metric: education_count=3"}
{"event": "custom_metric", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "59ee8056-0285-4b7f-bd92-138c14fdc932", "endpoint": "/resume", "timestamp": "2025-06-25T16:34:30.506974", "experience_count": 0, "message": "Custom metric: experience_count=0"}
{"event": "custom_metric", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "59ee8056-0285-4b7f-bd92-138c14fdc932", "endpoint": "/resume", "timestamp": "2025-06-25T16:34:30.506974", "total_processing_time": 31.212016820907593, "message": "Custom metric: total_processing_time=31.212016820907593"}
{"event": "request_complete", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "59ee8056-0285-4b7f-bd92-138c14fdc932", "endpoint": "/resume", "timestamp": "2025-06-25T16:34:30.509973", "total_time_seconds": 31.22250008583069, "status_code": 200, "message": "Request completed in 31.2225s with status 200"}
{"event": "request_start", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "5cede0f7-e3d7-4e25-b13c-c8865aafa4b4", "endpoint": "/", "timestamp": "2025-06-25T16:35:59.043124", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "5cede0f7-e3d7-4e25-b13c-c8865aafa4b4", "endpoint": "/", "timestamp": "2025-06-25T16:35:59.044205", "total_time_seconds": 0.0010805130004882812, "status_code": 200, "message": "Request completed in 0.0011s with status 200"}
{"event": "request_start", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "1fb53854-1517-45dc-9816-c28c936829d6", "endpoint": "/favicon.ico", "timestamp": "2025-06-25T16:35:59.110827", "message": "Request started for endpoint: /favicon.ico"}
{"event": "request_complete", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "1fb53854-1517-45dc-9816-c28c936829d6", "endpoint": "/favicon.ico", "timestamp": "2025-06-25T16:35:59.112089", "total_time_seconds": 0.0012619495391845703, "status_code": 404, "message": "Request completed in 0.0013s with status 404"}
{"event": "request_start", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "55a1a546-2879-40f3-bfd6-46ea123b2bd6", "endpoint": "/docs", "timestamp": "2025-06-25T16:36:01.855937", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "55a1a546-2879-40f3-bfd6-46ea123b2bd6", "endpoint": "/docs", "timestamp": "2025-06-25T16:36:01.856938", "total_time_seconds": 0.0010001659393310547, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "8392c9e0-550d-46c2-8e32-f15614b637f2", "endpoint": "/openapi.json", "timestamp": "2025-06-25T16:36:02.029853", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "8392c9e0-550d-46c2-8e32-f15614b637f2", "endpoint": "/openapi.json", "timestamp": "2025-06-25T16:36:02.041851", "total_time_seconds": 0.011998891830444336, "status_code": 200, "message": "Request completed in 0.0120s with status 200"}
{"event": "request_start", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "cda6a060-cc77-4d52-b07a-89dd6065263e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-25T16:36:30.305382", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "cda6a060-cc77-4d52-b07a-89dd6065263e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-25T16:36:30.331894", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "cda6a060-cc77-4d52-b07a-89dd6065263e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-25T16:36:30.331894", "file_size_bytes": 74312, "message": "Custom metric: file_size_bytes=74312"}
{"event": "custom_metric", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "cda6a060-cc77-4d52-b07a-89dd6065263e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-25T16:36:30.332895", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "cda6a060-cc77-4d52-b07a-89dd6065263e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-25T16:36:30.332895", "extracted_text_length": 1823, "message": "Custom metric: extracted_text_length=1823"}
{"event": "custom_metric", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "cda6a060-cc77-4d52-b07a-89dd6065263e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-25T16:36:30.332895", "file_processing_time": 0.022511720657348633, "message": "Custom metric: file_processing_time=0.022511720657348633"}
{"event": "request_complete", "session_id": "15d097eb-1c43-4d63-81bc-b080b108bc2d", "request_id": "cda6a060-cc77-4d52-b07a-89dd6065263e", "endpoint": "/hybrid_resume", "timestamp": "2025-06-25T16:36:43.428830", "total_time_seconds": 13.123447895050049, "status_code": 200, "message": "Request completed in 13.1234s with status 200"}
