=== RAW RESPONSE ===
Length: 3004
Source: Nahush_SDE.pdf
Timestamp: 1750249656.5293458
=== CONTENT ===
```json
{
    "name": "Nahush Tawlare",
    "email": "<EMAIL>",
    "phone": "+917972465878",
    "education": [
        {
            "degree": "Post Graduate Diploma In Advanced Computing",
            "institution": "CDAC ,Noida",
            "year": "09/2022 - 03/2023"
        },
        {
            "degree": "Bachelor of Engineering in Mechanical",
            "institution": "HVPM College of Engineering",
            "year": "06/2015 - 06/2019"
        }
    ],
    "skills": [
        "JQuery",
        "ASP.NET MVC",
        "LINQ",
        "ASP.NET Web API",
        "MySQL/MsSQL",
        "HTML5,CSS",
        "JavaScript",
        "AngularJS",
        ".NET Core",
        "C#",
        "MS Oﬃce",
        "WPF"
    ],
    "experience": [
        {
            "company_name": "Enternetz Engg Pvt. Ltd, Nagpur",
            "role": "Steel Detailer",
            "duration": "01/2021 - 05/2022",
            "key_responsibilities": "To Details the Structural parts using AUTOCAD, ZW-CAD,CADAIN etc. software's."
        },
        {
            "company_name": "LaMinds",
            "role": "Software Developer",
            "duration": "09/2023 - Present",
            "key_responsibilities": "Write clean, maintainable and eﬃcient code in C# and other relevant languages. Develop and maintain software applications using the .NET framework or .NET Core. Implement software solutions based on technical speciﬁcations and requirements. Identify and ﬁx bugs, errors and issues in code. Create, maintain, and optimize databases using SQL Server or other database management systems. Develop and implement database queries, stored procedures, and data access logic. Develop user interfaces using HTML, CSS, JavaScript, Ajax and jQuery."
        }
    ],
    "projects": [
        {
            "name": "CREDIT CARD COMPARE",
            "description": "Platform- Java, MySQL, Hibernate, SpringBoot, HTML, CSS, Thyemeleaf, JavaScript, jQuery. To Find the Best credit card for customer according to their requirement on basis of various factor like incomes, spendings, etc."
        }
    ],
    "certifications": [
        "Python for Beginners - Newton School (2024)"
    ],
    "domain_of_interest": [
        "Software Development"
    ],
    "languages_known": [
        "English (Full Professional Proﬁciency)",
        "Marathi (Limited Working Proﬁciency)",
        "Hindi (Professional Working Proﬁciency)"
    ],
    "achievements": [
        "HEAD OF NSS UNIT (06/2017 - 06/2018) To Manage All the activities of college under NSS unit, which helped me to grow Leadership and Team work Qualities",
        "Head of Magazine Carricature Section (06/2017 - 06/2018)",
        "Work Shop on PCB Designing"
    ],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": null,
    "personal_projects": [],
    "social_media": [
        "linkedin.com/in/nahushtawlare",
        "live:ntawlare",
        "github.com/nahushtawlare"
    ]
}
```