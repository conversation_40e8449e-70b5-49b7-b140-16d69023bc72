=== RAW RESPONSE ===
Length: 2941
Source: KARISHMA_KALAMBE_CV.pdf
Timestamp: 1750249595.7028863
=== CONTENT ===
```json
{
    "name": "KARISHMA KALAMBE",
    "email": "karishm<PERSON><EMAIL>",
    "phone": "+917083236288",
    "education": [
        {
            "degree": "B.E (Electronics Engineering)",
            "institution": "Kavikulguru Institute of Technology and Science, Ramtek",
            "year": "June 2019"
        },
        {
            "degree": "PG-DAC IACSD",
            "institution": "Pune",
            "year": "February 2021"
        },
        {
            "degree": "HSC",
            "institution": "Shirinbai Neterwala School, Maneck Nagar Tumsar",
            "year": "May 2015"
        },
        {
            "degree": "SSC",
            "institution": "Shirinbai Neterwala School, Maneck Nagar Tumsar",
            "year": "May 2013"
        }
    ],
    "skills": [
        "C",
        "C++",
        "C#",
        "ADO.NET",
        "LINQ",
        "Oracle",
        "MySQL",
        "MVC using .Net and .Net Core 2.0",
        "HTML",
        "CSS",
        "Bootstrap",
        "JavaScript",
        "jQuery",
        "Ajax"
    ],
    "experience": [
        {
            "company_name": "Cybage Software Pvt. ltd, Pune, India",
            "role": "Software Developer",
            "duration": "April 2021 - Current",
            "key_responsibilities": "I am currently working on Rakuten Advertising Project in which I had used C# and for DB Connectivity using ADO.Net and LINQ. Here we are using 2 frameworks as .Net 4.5 and .Net Core 2.0. Here we are using MVC architecture. The services which are included are REST API using .Net and also worked on Third -party API integration. I have performed following tasks while working in this project. Interacting with the client during requirement analysis. Handling one separate project with my colleagues. As per client requirement working on new Development, testing and Support for Project. Working as a .Net Developer. Working on MVC Architecture using .Net and Razor Controls. Working on third - party API integration in our project. Solved Design level issues, SQL and Production related issues. Working with JIRA and GITHUB. Working on SonarQube for Code analysis purpose."
        }
    ],
    "projects": [],
    "certifications": [
        "Python for Beginners - Newton School (2024)"
    ],
    "domain_of_interest": [
        "Software Development"
    ],
    "languages_known": [
        "English",
        "Hindi",
        "Marathi"
    ],
    "achievements": [
        "Dancing",
        "Exploring new places",
        "Basketball"
    ],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": "Innovative Software Developer with 3 years of hands -on experience designing and testing application. Well -versed in Agile methodologies and coding languages. Dedicated to developing creative solutions to meet business goals and requirements.",
    "personal_projects": [],
    "social_media": []
}
```