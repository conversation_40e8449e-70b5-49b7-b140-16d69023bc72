================================================================================
FINAL EXTRACTION RESULTS SUMMARY
================================================================================
Extraction Method: multiple_calls
Completed: 2025-06-17T17:42:16.415356
Processing Time: 81.8205497264862 seconds
Total LLM Calls: 8
Overall Confidence: 0.9750000000000001
Sections Found: 8
================================================================================

SECTION EXTRACTION RESULTS:
----------------------------------------
SUMMARY: ✅ SUCCESS
  Confidence: 1.00
  Content Length: 2309 characters
  Preview: PROFESSIONAL SUMMARY
● 7 years of experience in developing and maintaining strong backend applicatio...

EDUCATION: ✅ SUCCESS
  Confidence: 1.00
  Content Length: 129 characters
  Preview: EDUCATION

Stevens Institute of technology 
Hoboken, New Jersey, USA 
Master of Science in Computer ...

EXPERIENCE: ✅ SUCCESS
  Confidence: 1.00
  Content Length: 4111 characters
  Preview: EXPERIENCE

Wells Fargo
Java Developer Aug 2023 - Present
Project description :
Wells Fargo is a lea...

SKILLS: ✅ SUCCESS
  Confidence: 1.00
  Content Length: 663 characters
  Preview: SKILLS
● Programming Languages : Java, JavaScript, TypeScript, Python, C++, SQL
● Technologies: Grap...

PROJECTS: ✅ SUCCESS
  Confidence: 1.00
  Content Length: 4399 characters
  Preview: PROJECTS

Wells Fargo
Java Developer Aug 2023 - Present
Project description :
Wells Fargo is a leadi...

CERTIFICATIONS: ✅ SUCCESS
  Confidence: 0.90
  Content Length: 2387 characters
  Preview: PROFESSIONAL SUMMARY
● 7 years of experience in developing and maintaining strong backend applicatio...

ACHIEVEMENTS: ✅ SUCCESS
  Confidence: 1.00
  Content Length: 2091 characters
  Preview: ACHIEVEMENTS

● 7 years of experience in developing and maintaining strong backend applications usin...

LANGUAGES: ✅ SUCCESS
  Confidence: 0.90
  Content Length: 27 characters
  Content: LANGUAGES
English
NOT_FOUND

================================================================================
All individual LLM calls are logged in separate files in this folder.
Check the numbered call files for detailed prompt/response logs.
