{"name": "SHWETALI DHOMASE", "email": "<EMAIL>", "phone": "9579383426", "education": [{"degree": "Master of Computer Science", "institution": "SPPU University, Indira College Pune", "year": "2020 - 2022"}, {"degree": "Bachelor of Computer Science", "institution": "SPPU University, Agasti Arts,Commerce And <PERSON><PERSON><PERSON><PERSON> Science College", "year": "2017 - 2020"}], "skills": ["HTML", "CSS", "Bootstrap", "AJAX", "JavaScript", "j<PERSON><PERSON><PERSON>", "C-Sharp", "ASP.NET MVC", "ADO.NET", "LINQ", "Entity Framework", ".NET Core", "Web API", "MySQL", "SQL Server", "Problem Solving", "Team Collaboration", "Time Management", "Communication"], "experience": [{"company_name": "Tata Consultancy Service Thane, India", "role": "Software Engineer", "duration": "Sep 2022 - Dec 2023", "key_responsibilities": "Streamlined claims processing for a key insurance project, contributing to 100% efficiency improvement, by leveraging .NET tools for software development, optimization, and debugging. Collaborated actively with clients to diagnose and resolve real-time challenges, leading to better enhancement in overall project performance. Coordinated with QA and analysts to ensure seamless software delivery, resulting in 90% improvement in project efficiency and client satisfaction."}, {"company_name": "Freelance Remote", "role": "Web Design & Developer", "duration": "Aug 2022", "key_responsibilities": "Helped clients to acheive 80% more growth by using the application. Gathered feedback from clients and helps to improved the application performance by 90%. Developed responsive web applications using dot net technologies."}], "projects": [{"name": "Insurance Project", "description": "This is a .NET framework-based application designed to streamline the management of various insurance policies. It caters to a wide range of users, facilitating tasks such as policy creation, modification, renewal, and claims processing. Frontend HTML, CSS, Bootstrap, AJAX, JavaScript Backend C-Sharp, ASP.NET Database MYSQL"}, {"name": "Inventory System", "description": "The Inventory System is a freelance project developed to meet the specific needs of a client for managing inventory in their shop. Built to streamline inventory management processes, the system allows for easy tracking of stock levels, item details, and sales transactions. Frontend HTML, CSS, Bootstrap, AJAX, JQuery Backend C-Sharp, ASP.NET, ADO.NET, WEB API Database MYSQL"}], "certifications": [], "domain_of_interest": [], "languages_known": [], "achievements": ["Streamlined claims processing for a key insurance project, contributing to 100% efficiency improvement", "Developed responsive web applications using dot net technologies", "Helped clients to acheive 80% more growth by using the application", "Gathered feedback from clients and helps to improved the application performance by 90%"], "publications": [], "volunteer_experience": [], "references": [], "summary": "Software Engineer with 1+ years of experience in software design, analysis, development, testing and implementation of web and client-server architecture in multi-national company using Microsoft Technologies, seeking full-time developer roles.", "personal_projects": [], "social_media": ["linkedin.com/shwetali-dhomase", "shwetali262.github.io/PersonalWebsite/"]}