=== RAW RESPONSE ===
Length: 3100
Source: Updated_Sandeep_Resume.pdf
Timestamp: 1750250216.139037
=== CONTENT ===
```json
{
    "name": "Sandeep Verma",
    "email": "<EMAIL>",
    "phone": "+918898663071",
    "education": [
        {
            "degree": "Bachelor’s of Technology in Computer Science Engineering",
            "institution": "University of Mumbai, Mumbai",
            "year": "2016 - 2020"
        }
    ],
    "skills": [
        "C#",
        ".NET Framework (4.8)",
        ".NET Core (6,7)",
        "Entity Framework",
        "REST API",
        "ADO.net",
        "LINQ",
        "gRPC",
        "Microservices",
        "jQuery",
        "JavaScript",
        "AutoMapper",
        "Fluent Validation",
        "SQL",
        "MySQL",
        "Swagger Documentation",
        "TFS",
        "Windows",
        "Git",
        "Postman",
        "VS Code",
        "Visual Studio"
    ],
    "experience": [
        {
            "company_name": "Quantasis Private Ltd.",
            "role": "Junior .Net Developer",
            "duration": "Sept 2023 – Current",
            "key_responsibilities": "Developing and maintaining .NET applications, contributing to the design and implementation of RESTful APIs, working within Agile development methodologies, participating in code reviews, assisting with testing and deployment, and collaborating with senior developers to solve technical challenges."
        }
    ],
    "projects": [
        {
            "name": "Boylesports Pvt. Ltd.",
            "description": "Integrated Current MyAccount services into Universal MyAccount services, enhancing features such as wallet, summary, promotions, responsible gambling, inbox, and settings for Boylesports, a North-Ireland based gaming company specializing in gambling and betting services. Aiming to optimize user experience and drive company revenue."
        },
        {
            "name": "Salesforce Application",
            "description": "Contributed to the development of an enterprise-level supply chain management application, facilitating product inventory management and distribution across regions. Developed REST API utilizing mediator pattern in .NET Core 6."
        }
    ],
    "certifications": [
        "Python for Beginners - Newton School (2024)"
    ],
    "domain_of_interest": [
        "Supply chain management",
        "Wallet"
    ],
    "languages_known": [
        "English",
        "Hindi",
        "Bhojpuri",
        "Marathi"
    ],
    "achievements": [
        "Graduated with CGPA: 7.1"
    ],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": "A diligent .NET developer with over 6 months of experience in crafting intricate enterprise solutions leveraging Microsoft technologies. Proficient in web-based technologies such as .NET Core, REST API, C#, and Entity Framework. Possesses comprehensive expertise across the Software Development Life Cycle (SDLC), encompassing requirement analysis, design, development, testing, and deployment of large-scale applications.",
    "personal_projects": [],
    "social_media": [
        "linkedin.com/in/sandeep-verma-4b097914b"
    ]
}
```