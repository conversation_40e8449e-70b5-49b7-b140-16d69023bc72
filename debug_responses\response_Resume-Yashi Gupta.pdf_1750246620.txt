=== RAW RESPONSE ===
Length: 3339
Source: Resume-<PERSON><PERSON>.pdf
Timestamp: 1750246620.502828
=== CONTENT ===
```json
{
    "name": "<PERSON><PERSON>",
    "email": null,
    "phone": null,
    "education": [
        {
            "degree": "Bachelor of Technology (Computer Science)",
            "institution": "Newton School of Technology, Rishihood University",
            "year": "2023 - 2027"
        },
        {
            "degree": "Intermediate",
            "institution": "Huddard High School",
            "year": "2021 - 2022"
        },
        {
            "degree": "Matriculation",
            "institution": "Huddard High School",
            "year": "2019 - 2020"
        }
    ],
    "skills": [
        "Java",
        "JavaScript",
        "CSS",
        "HTML",
        "TypeScript",
        "Python",
        "React",
        "MySQL",
        "Express JS",
        "NodeJS",
        "Prisma ORM",
        "Tailwind",
        "Hadoop HDFS",
        "NextJS",
        "Tailwind CSS",
        "Firebase"
    ],
    "experience": [
        {
            "company_name": "IIT Roorkee with Social Studies Foundation",
            "role": "Frontend Intern",
            "duration": "June 2024 - August 2024",
            "key_responsibilities": "Developed in collaboration between IIT Roorkee and the Social Studies Foundation, this project curates and displays government schemes, job openings, and scholarships for the SC/ST community. Designed lter tabs with state-wise and department-wise options, integrated APIs for categorized schemes (scholarships, job openings, general schemes), and built responsive components like detailed scheme cards and homepage elements for improved user experience."
        }
    ],
    "projects": [
        {
            "name": "Expedition - Backend Python",
            "description": "Backend in Python for a ticket booking system, Expedition. Comprehensive CRUD APIs for managing data, secure user authentication, and ecient data handling for smooth ticket booking and selling system."
        },
        {
            "name": "iPhone 15 Pro Website",
            "description": "iPhone 15 Pro website replica with exceptional design and functionality. Developed a precise iPhone 15 Pro website replica featuring an advanced video carousel and enhanced user experience."
        },
        {
            "name": "FashMore-ECommerce-Project",
            "description": "Developed a modern e-commerce platform delivering the latest in fashion trends. Seamless browsing with organized categories, secure authentication, advanced search, ltering, and ecient cart management."
        }
    ],
    "certifications": [
        "Python for Beginners - Newton School (2024)"
    ],
    "domain_of_interest": [],
    "languages_known": [],
    "achievements": [
        "Participated in a hackathon organized by Google Cloud to develop an AI-powered chatbot website.",
        "Participated in HackCBS, the biggest student-led hackathon.",
        "Moderator at Coding Club, Newton School of Technology"
    ],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": "Full stack developer with a problem-solving mindset and strong skills in frontend, backend and DSA, ready to enhance user experiences in a team environment.",
    "personal_projects": [],
    "social_media": [
        "linkedin.com/in/yashigupta",
        "github.com/yashigupta"
    ]
}
```