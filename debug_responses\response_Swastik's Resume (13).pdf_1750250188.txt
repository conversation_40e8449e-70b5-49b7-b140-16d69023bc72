=== RAW RESPONSE ===
Length: 3928
Source: Swastik's Resume (13).pdf
Timestamp: 1750250188.1314697
=== CONTENT ===
```json
{
    "name": "<PERSON>wasti<PERSON> Awaze",
    "email": "<EMAIL>",
    "phone": "+918975025059",
    "education": [
        {
            "degree": "Post Graduate Diploma In Advanced Computing",
            "institution": "Centre for Development of Advanced Computing (CDAC), Mumbai",
            "year": "07/2022 - 05/2023"
        },
        {
            "degree": "Bachelor of Engineering- Mechanical",
            "institution": "SGBAU, Amravati University",
            "year": "06/2014 - 06/2018"
        },
        {
            "degree": "HSC",
            "institution": "Guru Nanak College of Science, Ballarpur",
            "year": "06/2013 - 04/2014"
        },
        {
            "degree": "SSC",
            "institution": "Nav Bharat Vidyalaya, Mul",
            "year": "06/2011 - 05/2012"
        }
    ],
    "skills": [
        "Java",
        "React.js",
        "Asp.Net",
        "Bootstrap",
        "MySQL",
        "MongoDB",
        "HTML",
        "CSS",
        "JavaScript",
        "Hibernate",
        "Spring Boot",
        ".NET Framework",
        "DSA",
        "GitHub",
        "J2EE",
        "DevOps"
    ],
    "experience": [
        {
            "company_name": "kjss cosmos pvt. ltd.",
            "role": "dot net developer",
            "duration": "09/2023 - 03/2024",
            "key_responsibilities": "Working on two live project Ecommerce portal and News portal."
        }
    ],
    "projects": [
        {
            "name": "MyBank (Bank Management System)",
            "description": "MyBank is a WebApp that aims to provide a simple and eﬃcient banking solution developed. The WebbApp oﬀers various features, including account creation, fund transfer, and balance inquiry. MyBank is designed to be user-friendly and easily accessible, with a straightforward interface that simpliﬁes banking operations. Platform- ReactJS, Spring Boot, MYSQL"
        },
        {
            "name": "FitnessFrek",
            "description": "The application provides a user-friendly interface where users can create personalized proﬁles and input their ﬁtness-related information such as height, weight, age, and desired ﬁtness goals. Based on this information, FitnessFrek generates customized workout plans and nutrition recommendations to help users achieve their objectives. Platform- ReactJS, NodeJS, MYSQL"
        },
        {
            "name": "User Management System",
            "description": "The User Management System, administrators can create and manage user accounts, assigning diﬀerent roles and permissions based on the user's responsibilities and level of access required. This helps maintain data security and restricts unauthorized access to sensitive information. The system also includes features such as password management, allowing users to reset their passwords or change them periodically for enhanced security. Platform-DotNet"
        }
    ],
    "certifications": [
        "Python for Beginners - Newton School (2024)",
        "Project Leader in DAC course by CDAC Juhu December 2022 — April 2023",
        "Selected as best paper presented in National Conference on Quality Upgradation in Engineering, Science and Technology"
    ],
    "domain_of_interest": [
        "Coding and Programming",
        "Technology Trends",
        "Professional Development",
        "Open Source Contributions",
        "Problem Solving",
        "Technology Enthusiast",
        "Continuous Learning"
    ],
    "languages_known": [
        "English (Full Professional Proﬁciency)",
        "Hindi (Full Professional Proﬁciency)",
        "Marathi (Native or Bilingual Proﬁciency)"
    ],
    "achievements": [
        "Selected as best paper presented in National Conference on Quality Upgradation in Engineering, Science and Technology"
    ],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": null