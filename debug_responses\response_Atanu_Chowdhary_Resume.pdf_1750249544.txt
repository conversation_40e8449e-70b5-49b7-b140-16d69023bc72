=== RAW RESPONSE ===
Length: 3697
Source: Atanu_Chowdhary_Resume.pdf
Timestamp: 1750249544.8879228
=== CONTENT ===
```json
{
    "name": "ATANU CHOWDHARY",
    "email": "<EMAIL>",
    "phone": "+91 9284593749",
    "education": [
        {
            "degree": "BSc (Computer Science)",
            "institution": "Pune University",
            "year": "2019 - 2022"
        }
    ],
    "skills": [
        ".NET",
        "ASP.NET",
        "ASP.NET MVC",
        "C#",
        "Angular",
        "Web APIs",
        "Entity Framework",
        "Azure DevOps",
        "SQL Server",
        "HTML",
        "CSS",
        "JavaScript",
        "PHP"
    ],
    "experience": [
        {
            "company_name": "LTIMindtree",
            "role": "Software Engineer",
            "duration": "Aug 2019 - Current",
            "key_responsibilities": "Worked on various .NET technologies such as ASP.NET, C#, MVC, and Web API to develop and maintain complex software applications. Resolved and closed numerous tickets by providing timely and effective technical support to customers resulting in customer satisfaction by 80%. Fixed a major issue faced by application during a DR activity. Fixed a major production issue of xml parsing resulting in reduction in tickets by 60%."
        },
        {
            "company_name": "BasketHunt",
            "role": "Web Developer(Intern)",
            "duration": "Nov 2021 - Jan 2022",
            "key_responsibilities": "Resolved over 100 tickets related to webpage issues within a tight deadline, maintaining a satisfaction rate of 90% from internal and external stakeholders. Developed and maintained company websites using HTML, CSS, JavaScript, and PHP, ensuring high quality and userfriendly interfaces. Documented website development processes and procedures, enabling future developers to easily maintain and update the website."
        }
    ],
    "projects": [
        {
            "name": "NotionDocs",
            "description": "A note-taking and document management application built with .NET 8. Features rich-text notes, cross-platform accessibility, and advanced editing tools. Utilizes ASP.NET Core and .NET 8 for a seamless user experience."
        },
        {
            "name": "ChatVista",
            "description": "A real-time chat application developed to enhance skills in web development and communication. Incorporates SignalR for real-time messaging and Angular for a responsive frontend backend architecture built with .NET 7 for scalability and performance."
        },
        {
            "name": "Movie Booking",
            "description": "An online platform for booking movies, developed with .NET 5, Entity Framework, and Razor Pages, integrates PayPal API for secure payments and includes an admin panel for content management."
        }
    ],
    "certifications": [
        "Python for Beginners - Newton School (2024)"
    ],
    "domain_of_interest": [
        "Web Development",
        "Software Engineering"
    ],
    "languages_known": [
        "English",
        "Hindi"
    ],
    "achievements": [
        "Code To Contribute(GeeksForGeeks) : Ranked under to 300 Contributors in geeksforgeeks code to contribute contest.",
        "Intercollege Project Competition : Won Intercollege Project Competition for Self-Driving vehicle."
    ],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": "A dedicated software developer with a passion for learning and solving complex problems. Seeking a challenging role where I can contribute my technical skills and collaborate with a dynamic team",
    "personal_projects": [],
    "social_media": [
        "linkedin.com/atanuchowdhary36698",
        "github.com/atanuchowdhary36698"
    ]
}
```