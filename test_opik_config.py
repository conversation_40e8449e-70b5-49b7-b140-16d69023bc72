#!/usr/bin/env python3
"""
Test script to verify Opik configuration and basic functionality.
This script tests the Opik setup without requiring the full API server.
"""

import os
import sys
import time

# Add the current directory to the path so we can import from main.py
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_opik_imports():
    """Test that Opik can be imported correctly."""
    try:
        import opik
        from opik import track, opik_context
        print("✅ Opik imports successful")
        return True
    except ImportError as e:
        print(f"❌ Opik import failed: {e}")
        return False

def test_opik_client_creation():
    """Test that Opik client can be created with our configuration."""
    try:
        import opik
        import ssl

        # Fix SSL certificate issues for Windows
        ssl._create_default_https_context = ssl._create_unverified_context

        # Set environment variables
        os.environ["OPIK_API_KEY"] = "FxxYR5XJJ9lACASGJ677QE3a7"
        os.environ["OPIK_PROJECT_NAME"] = "Gemma api"
        os.environ["OPIK_URL_OVERRIDE"] = "https://www.comet.com/opik/api"

        # Test client creation using environment variables
        client = opik.Opik()
        print("✅ Opik client created successfully")
        return True, client
    except Exception as e:
        print(f"❌ Opik client creation failed: {e}")
        return False, None

def test_opik_decorator():
    """Test that the @track decorator works correctly."""
    try:
        from opik import track
        import ssl

        # Fix SSL certificate issues for Windows
        ssl._create_default_https_context = ssl._create_unverified_context

        # Set environment variables
        os.environ["OPIK_API_KEY"] = "FxxYR5XJJ9lACASGJ677QE3a7"
        os.environ["OPIK_PROJECT_NAME"] = "Gemma api"
        os.environ["OPIK_URL_OVERRIDE"] = "https://www.comet.com/opik/api"

        @track(name="test_function", project_name="Gemma api")
        def test_function(input_text):
            """A simple test function to verify Opik tracking."""
            return f"Processed: {input_text}"

        # Call the decorated function
        result = test_function("Hello, Opik!")
        print(f"✅ Opik decorator test successful: {result}")
        return True
    except Exception as e:
        print(f"❌ Opik decorator test failed: {e}")
        return False

def test_opik_trace_creation():
    """Test creating a manual trace with Opik."""
    try:
        import opik
        import ssl

        # Fix SSL certificate issues for Windows
        ssl._create_default_https_context = ssl._create_unverified_context

        # Set environment variables
        os.environ["OPIK_API_KEY"] = "FxxYR5XJJ9lACASGJ677QE3a7"
        os.environ["OPIK_PROJECT_NAME"] = "Gemma api"
        os.environ["OPIK_URL_OVERRIDE"] = "https://www.comet.com/opik/api"

        client = opik.Opik()
        
        # Create a test trace
        trace = client.trace(
            name="test_trace",
            input={"test_input": "Hello, World!"},
            output={"test_output": "Hello, Opik!"},
            tags=["test", "integration", "gemma_api"]
        )
        
        # Add a span to the trace
        span = trace.span(
            name="test_span",
            type="llm",
            input={"prompt": "Test prompt"},
            output={"response": "Test response"}
        )
        
        # End the trace and span
        span.end()
        trace.end()
        
        print("✅ Manual trace creation successful")
        return True, client
    except Exception as e:
        print(f"❌ Manual trace creation failed: {e}")
        return False, None

def test_opik_feedback_scores():
    """Test adding feedback scores to traces."""
    try:
        import opik
        import ssl

        # Fix SSL certificate issues for Windows
        ssl._create_default_https_context = ssl._create_unverified_context

        # Set environment variables
        os.environ["OPIK_API_KEY"] = "FxxYR5XJJ9lACASGJ677QE3a7"
        os.environ["OPIK_PROJECT_NAME"] = "Gemma api"
        os.environ["OPIK_URL_OVERRIDE"] = "https://www.comet.com/opik/api"

        client = opik.Opik()
        
        # Create a trace with feedback scores
        trace = client.trace(
            name="test_trace_with_feedback",
            input={"test_input": "Test input"},
            output={"test_output": "Test output"}
        )
        
        # Add feedback scores
        client.log_traces_feedback_scores(
            scores=[
                {
                    "id": trace.id,
                    "name": "quality",
                    "value": 0.95,
                    "reason": "High quality test response"
                },
                {
                    "id": trace.id,
                    "name": "accuracy",
                    "value": 0.88,
                    "reason": "Good accuracy for test case"
                }
            ]
        )
        
        trace.end()
        print("✅ Feedback scores test successful")
        return True, client
    except Exception as e:
        print(f"❌ Feedback scores test failed: {e}")
        return False, None

def main():
    """Main test function."""
    print("🧪 Opik Configuration Test Suite")
    print("=" * 50)
    
    # Test 1: Import Opik
    print("\n1. Testing Opik imports...")
    import_success = test_opik_imports()
    
    if not import_success:
        print("❌ Cannot proceed without successful Opik imports")
        return
    
    # Test 2: Create Opik client
    print("\n2. Testing Opik client creation...")
    client_success, client = test_opik_client_creation()
    
    # Test 3: Test decorator
    print("\n3. Testing Opik decorator...")
    decorator_success = test_opik_decorator()
    
    # Test 4: Test manual trace creation
    print("\n4. Testing manual trace creation...")
    trace_success, _ = test_opik_trace_creation()
    
    # Test 5: Test feedback scores
    print("\n5. Testing feedback scores...")
    feedback_success, _ = test_opik_feedback_scores()
    
    # Flush traces
    if client:
        try:
            print("\n6. Flushing traces...")
            client.flush()
            print("✅ Traces flushed successfully")
        except Exception as e:
            print(f"⚠️  Warning: Failed to flush traces: {e}")
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"✅ Imports: {'PASSED' if import_success else 'FAILED'}")
    print(f"✅ Client Creation: {'PASSED' if client_success else 'FAILED'}")
    print(f"✅ Decorator: {'PASSED' if decorator_success else 'FAILED'}")
    print(f"✅ Manual Traces: {'PASSED' if trace_success else 'FAILED'}")
    print(f"✅ Feedback Scores: {'PASSED' if feedback_success else 'FAILED'}")
    
    all_passed = all([import_success, client_success, decorator_success, trace_success, feedback_success])
    
    if all_passed:
        print("\n🎉 All tests passed! Opik configuration is working correctly.")
        print("\n📈 Check your Opik dashboard at: https://www.comet.com/opik")
        print("   Project: 'Gemma api'")
        print("   You should see test traces from this script.")
        print("\n🚀 You can now start the main API server:")
        print("   python main.py")
    else:
        print("\n❌ Some tests failed. Please check the configuration.")
        print("   - Verify your API key is correct")
        print("   - Check your internet connection")
        print("   - Ensure you have access to the Comet platform")

if __name__ == "__main__":
    main()
