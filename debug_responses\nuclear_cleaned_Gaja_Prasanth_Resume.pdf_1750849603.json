{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "+************", "education": [{"degree": "B.Tech: CSE (Artificial Intelligence And Machine Learning)", "institution": "<PERSON>dra Engineering And Technology - Porur, India", "year": "2021-2025"}], "skills": ["Python", "NLP", "SQL", "Text Analytics", "Machine Learning", "Deep Learning", "Flask", "HTML", "CSS"], "experience": [{"company_name": "SRET JUN ’22 – JUL ’22", "role": "Student Intern", "duration": "June 2022 – July 2022", "key_responsibilities": "Implemented a leaf disease detection project using VGG19, employing data augmentation and transfer learning to enhance model performance."}, {"company_name": "Qwings IIT Madras RP", "role": "Student Intern", "duration": "August 2023 – October 2023", "key_responsibilities": "Integrated multiple machine learning projects into website using Flask for seamless model deployment and user interaction."}, {"company_name": "Qwings IIT Madras RP", "role": "Web Development Instructor", "duration": "May 2024 – June 2024", "key_responsibilities": "Taught fundamental web development concepts to children, focusing on HTML and CSS. Received positive feedback from students and parents, contributing to the company’s community outreach and educational initiatives."}], "projects": [{"name": "Leaf Disease Detection", "description": "I developed a leaf disease detection model using VGG19, achieving high accuracy in classifying plant diseases for improved agricultural monitoring."}, {"name": "Neural Style Transfer", "description": "I implemented Arbitrary Neural Style Transfer using TensorFlow Hub’s model to apply diverse artistic styles to images."}, {"name": "Medical Transcript Classification", "description": "I developed a medical transcript classification system using N-grams and various supervised machine learning algorithms, achieving accurate categorization of clinical texts."}], "certifications": ["Python for Beginners - Newton School (2024)"], "domain_of_interest": ["Artificial Intelligence", "Machine Learning", "Deep Learning"], "languages_known": ["English", "Tamil"], "achievements": ["Received positive feedback from students and parents, contributing to the company’s community outreach and educational initiatives."], "publications": [], "volunteer_experience": [], "references": [], "summary": "Passionate developer with interests in creating user-friendly web application, Eager to contribute and create innovative solutions.", "personal_projects": [], "social_media": ["Github: Gaja1595"]}