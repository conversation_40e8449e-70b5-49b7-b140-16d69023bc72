================================================================================
LLM CALL LOG - 2025-06-25 16:56:40
================================================================================

[CALL INFORMATION]
Endpoint: /hybrid_resume
Context: Resume-Raman <PERSON>.pdf
Call Type: main
Model: gemma3:4b
Timestamp: 2025-06-25T16:56:40.699903
Metadata: {
  "timeout_seconds": 120,
  "max_tokens": 1000,
  "processing_time": 17.39149785041809,
  "has_image": false,
  "prompt_length": 5970,
  "response_length": 3152,
  "eval_count": 839,
  "prompt_eval_count": 1492,
  "model_total_duration": 17340435700
}

[PROMPT]
Length: 5970 characters
----------------------------------------

    FORBIDDEN RESPONSES - READ THIS FIRST:
    - Do NOT use ```json or ``` or any markdown formatting
    - Do NOT add explanations, comments, or extra text
    - Do NOT use code blocks or backticks
    - Start IMMEDIATELY with { (opening brace)
    - End IMMEDIATELY with } (closing brace)
    - Return ONLY the JSON object, nothing else

    You are an expert resume parser. Extract ALL information from the resume sections below and return it as a clean JSON object.

    CRITICAL SCHEMA REQUIREMENTS:
    1. Extract ALL information that is explicitly mentioned in the resume sections.
    2. Format your response as a valid JSON object with EXACTLY the following structure:

    {
        "name": "Full Name",
        "email": "<EMAIL>" or null,
        "phone": "+1234567890" or null,
        "education": [
            {
                "degree": "Full Degree Name (Including Specialization)",
                "institution": "Institution Name",
                "year": "Year or Date Range"
            }
        ],
        "skills": ["Skill 1", "Skill 2", "Skill 3", ...],
        "experience": [
            {
                "company_name": "Company Name with Location if mentioned",
                "role": "Job Title",
                "duration": "Date Range",
                "key_responsibilities": "Detailed description of responsibilities and achievements"
            }
        ],
        "projects": [
            {
                "name": "Project Name",
                "description": "Detailed project description including technologies used"
            }
        ],
        "certifications": ["Certification Name 1", "Certification Name 2", ...],
        "domain_of_interest": ["Interest 1", "Interest 2", ...],
        "languages_known": ["Language 1", "Language 2", ...],
        "achievements": ["Achievement 1", "Achievement 2", ...],
        "publications": ["Publication 1", "Publication 2", ...],
        "volunteer_experience": ["Volunteer Experience 1", "Volunteer Experience 2", ...],
        "references": [],
        "summary": "Summary text or null",
        "personal_projects": [],
        "social_media": ["platform1.com/username", "platform2.com/username"]
    }

    STRICT FORMATTING RULES:
    3. For arrays, if no information is available, use an empty array []
    4. For string fields, if no information is available, use null
    5. Do not make up or infer information that is not explicitly stated in the resume
    6. Ensure the JSON is properly formatted and valid
    7. CRITICAL: Keep skills as a simple array of strings, not as objects or dictionaries
    8. CRITICAL: Keep certifications as a simple array of strings, not as objects
    9. CRITICAL: Keep achievements as a simple array of strings, not as objects
    10. CRITICAL: For experience entries, include all details in the key_responsibilities field as a single string with line breaks (\n)
    11. CRITICAL: For projects, include all details in the description field as a single string with line breaks (\n)
    12. CRITICAL: Extract the name, email, and phone from the CONTACT INFORMATION section if available

    CONTENT CLASSIFICATION RULES:
    13. EXPERIENCE section should contain ONLY professional work experience with companies/organizations
    14. EXTRA-CURRICULAR ACTIVITIES, sports, competitions, awards should go in ACHIEVEMENTS array
    15. CERTIFICATIONS should be simple strings like "Python for Beginners - Newton School (2024)"
    16. Do NOT create experience entries for activities that are not professional work
    17. Personal activities, sports, competitions, olympiads should be in achievements, not experience

    Resume Sections:
    CONTACT INFORMATION:
Raman Luhach
LinkedIn Github CodeChef Codeforces Leetcode

SUMMARY:
Dedicated and enthusiastic Front End Developer with a strong aptitude for coding and a passion for creating exceptional user
experiences.

EDUCATION:
Bachelor of Technology (AI ML) 2023 - 2027
Newton School of Technology , Rishihood University Grade: 9.18/10.0
Intermediate (Class XII) 2021 - 2022
Mother India Sr Sec School Marot, Jhajjar , Haryana Grade: 90.0%
Matriculation (Class X) 2019 - 2020
R E D Sr Sec School Chhuchhakwas ,Jhajjar , Haryana Grade: 95.8%

SKILLS:
Computer Languages: SQL, Java, JavaScript, CSS, HTML, Python
Software Packages: React, MySQL, NodeJS, Prisma ORM, Tailwind
Additional Courses: Data Structure
Soft Skills: Communication Skills, Research, Decision-making, Team Building, Leadership
EXTRA-CURRICULAR ACTIVITIES
Tech Lead atGoogle Developer Groups (GDG) Rishihood University.
Solved more then 400 questions on LeetCode .
Open source contribution inWikiMedia foundation and plone /volto .
1053 Rank inIEEEXtreme contest.
Holds highest ratings of 1592 on CodeChef ,1618 on LeetCode , and 1211 on Codeforces .
Attended and completed a university robotics workshop , creating a gesture-controlled wheelchair .
Plays basket and badminton. Achieved 2nd place in an inter-university kabaddi tournament .

PROJECTS:
Tech Talks , ( Github ) September 2024
Tech Stack :React ,Node .js,HTML /CSS
Feature :Authentication , blog management, categorized content ,CRUD operations on Blogs
Description : Developed "Tech Talks", a blogging site for tech content. Users can register, log in, create , access
and comment on tech blogs .
Zee5 Clone , ( Github ) ( Demo ) April 2024
Tech Stack :React ,HTML /CSS andJavaScript
Description : Created a Zee5 clone using React, HTML/CSS, and JavaScript. Users register, sign in securely, and access
a library of movies with intuitive UI .
Feature : Movie rendering , Authentication .

CERTIFICATIONS:
Robotics Workshop , Newton School of Technology, Rishihood University ( Link ) January 2024
Participated in a 5-day workshop on Arduino Uno fundamentals, DC motor functions, and joystick operations.
Developed a functional gesture-controlled wheelchair prototype.



    JSON object (start with { immediately):
    
----------------------------------------

[RESPONSE]
Length: 3152 characters
----------------------------------------
```json
{
    "name": "Raman Luhach",
    "email": null,
    "phone": null,
    "education": [
        {
            "degree": "Bachelor of Technology (AI ML)",
            "institution": "Newton School of Technology, Rishihood University",
            "year": "2023 - 2027"
        },
        {
            "degree": "Intermediate",
            "institution": "Mother India Sr Sec School Marot, Jhajjar , Haryana",
            "year": "2021 - 2022"
        },
        {
            "degree": "Matriculation",
            "institution": "R E D Sr Sec School Chhuchhakwas ,Jhajjar , Haryana",
            "year": "2019 - 2020"
        }
    ],
    "skills": [
        "SQL",
        "Java",
        "JavaScript",
        "CSS",
        "HTML",
        "Python",
        "React",
        "MySQL",
        "NodeJS",
        "Prisma ORM",
        "Tailwind",
        "Data Structure"
    ],
    "experience": [
        {
            "company_name": "Google Developer Groups (GDG) Rishihood University",
            "role": "Tech Lead",
            "duration": null,
            "key_responsibilities": "Led the GDG group at Rishihood University, organizing tech talks and workshops."
        }
    ],
    "projects": [
        {
            "name": "Tech Talks",
            "description": "Developed \"Tech Talks\", a blogging site for tech content. Users can register, log in, create, access, and comment on tech blogs. Tech Stack: React, Node.js, HTML/CSS. Feature: Authentication, blog management, categorized content, CRUD operations on Blogs."
        },
        {
            "name": "Zee5 Clone",
            "description": "Created a Zee5 clone using React, HTML/CSS, and JavaScript. Users register, sign in securely, and access a library of movies with intuitive UI. Feature: Movie rendering, Authentication."
        }
    ],
    "certifications": [
        "Robotics Workshop, Newton School of Technology, Rishihood University ( Link ) January 2024. Participated in a 5-day workshop on Arduino Uno fundamentals, DC motor functions, and joystick operations. Developed a functional gesture-controlled wheelchair prototype."
    ],
    "domain_of_interest": [
        "AI",
        "ML",
        "Web Development"
    ],
    "languages_known": [
        "English",
        "Hindi"
    ],
    "achievements": [
        "Solved more then 400 questions on LeetCode",
        "Holds highest ratings of 1592 on CodeChef ,1618 on LeetCode , and 1211 on Codeforces",
        "1053 Rank inIEEEXtreme contest",
        "Achieved 2nd place in an inter-university kabaddi tournament",
        "Developed a functional gesture-controlled wheelchair prototype",
        "Participated in a 5-day workshop on Arduino Uno fundamentals, DC motor functions, and joystick operations."
    ],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": "Dedicated and enthusiastic Front End Developer with a strong aptitude for coding and a passion for creating exceptional user experiences.",
    "personal_projects": [],
    "social_media": [
        "Github",
        "CodeChef",
        "Codeforces",
        "Leetcode"
    ]
}
```
----------------------------------------

================================================================================