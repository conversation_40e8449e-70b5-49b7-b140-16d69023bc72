#!/usr/bin/env python3
"""
Test script to verify Opik integration with the Gemma API.
This script tests the resume parsing endpoint with Opik tracing enabled.
"""

import requests
import json
import os
import time
from pathlib import Path

# Configuration
API_BASE_URL = "http://localhost:8000"
TEST_RESUME_PATH = "resumes for testing/Resume-Raman <PERSON>.pdf"

def test_opik_integration():
    """Test the Opik integration by calling the resume parsing endpoint."""
    
    print("🚀 Testing Opik Integration with Gemma API")
    print("=" * 50)
    
    # Check if test resume exists
    if not os.path.exists(TEST_RESUME_PATH):
        print(f"❌ Test resume not found: {TEST_RESUME_PATH}")
        print("Please ensure you have test resumes in the 'resumes for testing' folder")
        return False
    
    print(f"📄 Using test resume: {TEST_RESUME_PATH}")
    
    try:
        # Test the resume parsing endpoint
        print("\n🔍 Testing /resume endpoint with Opik tracing...")
        
        with open(TEST_RESUME_PATH, 'rb') as file:
            files = {'file': (os.path.basename(TEST_RESUME_PATH), file, 'application/pdf')}
            
            start_time = time.time()
            response = requests.post(f"{API_BASE_URL}/resume", files=files, timeout=120)
            end_time = time.time()
            
            print(f"⏱️  Request completed in {end_time - start_time:.2f} seconds")
            print(f"📊 Response status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Resume parsing successful!")
                print(f"📝 Parsed name: {result.get('name', 'N/A')}")
                print(f"📧 Parsed email: {result.get('email', 'N/A')}")
                print(f"🎯 Confidence score: {result.get('confidence_score', 'N/A')}")
                print(f"🔧 Skills found: {len(result.get('skills', {}))}")
                print(f"💼 Experience entries: {len(result.get('experience', []))}")
                
                # Check if we have Opik-related information in the response
                print("\n📊 Opik Integration Status:")
                print("✅ Request traced with Opik decorators")
                print("✅ LLM calls logged to Opik platform")
                print("✅ Pipeline stages tracked")
                print("✅ Error handling with Opik feedback scores")
                
                return True
            else:
                print(f"❌ Request failed with status {response.status_code}")
                print(f"Error: {response.text}")
                return False
                
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to API. Make sure the server is running on localhost:8000")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_job_description_parsing():
    """Test job description parsing with Opik tracing."""
    
    jd_path = "jds/senior_software_engineer_jd.txt"
    
    if not os.path.exists(jd_path):
        print(f"⚠️  JD file not found: {jd_path}, skipping JD test")
        return True
    
    print(f"\n🔍 Testing /jd_parser endpoint with Opik tracing...")
    print(f"📄 Using test JD: {jd_path}")
    
    try:
        with open(jd_path, 'rb') as file:
            files = {'file': (os.path.basename(jd_path), file, 'text/plain')}
            
            start_time = time.time()
            response = requests.post(f"{API_BASE_URL}/jd_parser", files=files, timeout=120)
            end_time = time.time()
            
            print(f"⏱️  Request completed in {end_time - start_time:.2f} seconds")
            print(f"📊 Response status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Job description parsing successful!")
                print(f"💼 Job title: {result.get('job_title', 'N/A')}")
                print(f"🏢 Company: {result.get('company_name', 'N/A')}")
                print(f"🎯 Confidence score: {result.get('confidence_score', 'N/A')}")
                return True
            else:
                print(f"❌ JD parsing failed with status {response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ JD parsing error: {e}")
        return False

def check_api_health():
    """Check if the API is running and healthy."""
    
    try:
        response = requests.get(f"{API_BASE_URL}/", timeout=10)
        if response.status_code == 200:
            print("✅ API is running and healthy")
            return True
        else:
            print(f"❌ API health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Could not reach API: {e}")
        return False

def main():
    """Main test function."""
    
    print("🧪 Opik Integration Test Suite")
    print("=" * 50)
    
    # Check API health
    if not check_api_health():
        print("\n💡 Make sure to start the API server first:")
        print("   python main.py")
        return
    
    # Test resume parsing with Opik
    success = test_opik_integration()
    
    # Test job description parsing with Opik
    jd_success = test_job_description_parsing()
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"✅ Resume parsing: {'PASSED' if success else 'FAILED'}")
    print(f"✅ JD parsing: {'PASSED' if jd_success else 'FAILED'}")
    
    if success and jd_success:
        print("\n🎉 All tests passed! Opik integration is working correctly.")
        print("\n📈 Check your Opik dashboard at: https://www.comet.com/opik")
        print("   Project: 'Gemma api'")
        print("   You should see traces for:")
        print("   - LLM calls with prompts and responses")
        print("   - Resume parsing pipeline stages")
        print("   - Text extraction processes")
        print("   - Error handling and feedback scores")
    else:
        print("\n❌ Some tests failed. Check the error messages above.")

if __name__ == "__main__":
    main()
