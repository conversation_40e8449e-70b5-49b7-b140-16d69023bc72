=== RAW RESPONSE ===
Length: 4235
Source: Adit-Jain-Resume .pdf
Timestamp: 1750249377.6852207
=== CONTENT ===
```json
{
    "name": "ADIT JAIN",
    "email": "<EMAIL>",
    "phone": "+91 8095011293",
    "education": [
        {
            "degree": "Bachelor of Computer Science",
            "institution": "Washington State University, USA",
            "year": "December 2021"
        }
    ],
    "skills": [
        "ASP.NET",
        ".NET framework",
        "REST API",
        "API Development",
        "Node.js",
        "Docker",
        "CI/CD",
        "SQL",
        "MSSQL",
        "MongoDB",
        "HTML",
        "CSS",
        "JavaScript",
        "React.js",
        "JQuery",
        "Kendo UI",
        "C#",
        "Java",
        "Python",
        "C++",
        "Object oriented programming(OOPS)",
        "SOLID Principles",
        "Git/Github"
    ],
    "experience": [
        {
            "company_name": "TEOCO Virginia, USA",
            "role": "Software Engineer",
            "duration": "May 2023 - Sept 2023",
            "key_responsibilities": "Developed full stack web application that did in depth data analysis and reporting using Kendo UI, REST APIs in ASP.NET.\nCreated several SQL queries for efficient data retrieval."
        },
        {
            "company_name": "Micron Technology Boise, ID, USA",
            "role": "IT Software Engineer",
            "duration": "Jan 2022 - March 2023",
            "key_responsibilities": "Developed several web applications that supports company’s memory and storage solutions in collaborative environment using ASP.NET and .Net framework.\nEnhanced testing efficiency through the implementation of an automation solution, resulting in a substantial 30% reduction in test suite execution time.\nManaged local Docker deployments and created local clusters\nResolved several bugs through meticulous log tracing and use of POSTMAN requests.\nFacilitated coordination with cross-functional teams spanning across North America and Asia, ensuring seamless deployment of several software releases.\nCreated and enhanced SQL Store procedures."
        },
        {
            "company_name": "Puzzl California, USA",
            "role": "Software Engineer intern",
            "duration": "June 2021 - Sept 2021",
            "key_responsibilities": "Contributed to a startup during its formative stages.\nEngineered backend services by leveraging several APIs, including the Stripe API, Facebook Graph API.\nCreated several REST API’s using C# and ASP.NET."
        }
    ],
    "projects": [
        {
            "name": "Airport of the future - Alaska Airlines",
            "description": "Created a web system to register user complaints for Alaska Airlines in a team of 5, developed a web system from scratch using React, Node, MongoDB, Azure to digitalize an existing paper form to decrease human error and increase submission efficiency by 30%. System deployed to Microsoft Azure and in use by Alaska Airlines CRO’s (Complaint Resolution officer) across United States."
        },
        {
            "name": "Calories Tracker Application",
            "description": "Build a project that track calories consumed by an user. User can input food consumed and using Edanum API it will show the nutrients info of that item, user can track everyday progress and set a nutrients goal. Application built using React.js, Node.js, MongoDB, Edanum API (See it here!)"
        },
        {
            "name": "Travel Package Application",
            "description": "Build a project that enables travel agencies to manage the itinerary and passengers of their travel packages. Each itinerary has set of destinations and each destination has set of activities that different type of passengers can sign up for. Applciation build using Java. Used SOLID principles and OOPS concepts. (See it here!)"
        },
        {
            "name": "Spreadsheet Application",
            "description": "Build a Microsoft excel mock up Spreadsheet application. Features included: cells can reference each other, calculate arithmetic operations, user can undo redo, save and load an excel sheet instance from XML file. Build using C#, .Net, WinForms. Used SOLID principles and OOPS concepts. (See it here!)"
        }
    ],
    "certifications": [