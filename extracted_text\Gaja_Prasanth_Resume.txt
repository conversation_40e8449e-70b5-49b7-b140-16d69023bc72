# Extracted Text Debug File
# Source File: Gaja_Prasanth_Resume.pdf
# Context: hybrid_resume_parsing
# Extraction Method: pdf_text
# Timestamp: 2025-06-25 16:36:30
# Text Length: 1823 characters
# ================================================

V Gaja Prasanth
Software Developer
Chennai,Tamil Nadu ·
<EMAIL> ·9080540705 ·Github: Gaja1595
LinkedIn: Gaja Prasanth ·
Summary
•Passionate developer with interests in creating user-friendly web application, Eager to contribute and create innova-
tive solutions.
EDUCATION
•B.Tech: CSE (Artificial Intelligence And Machine Learning) (2021-2025)
Sri Ramachandra Engineering And Technology - Porur, India
CGPA: [8.9]
Work History
•Student Intern, SRET JUN ’22 – JUL ’22
I implemented a leaf disease detection project using VGG19, employing data augmentation and
transfer learning to enhance model performance.
•Student Intern, Qwings IIT Madras RP AUG ’23 – OCT ’23
Integrated multiple machine learning projects into website using Flask for seamless model de-
ployment and user interaction.
•Web Development Instructor, Qwings IIT Madras RP MAY ’24 – JUN ’24
Taught fundamental web development concepts to children, focusing on HTML and CSS.
Received positive feedback from students and parents, contributing to the company’s community
outreach and educational initiatives.
PROJECTS
•Leaf Disease Detection
I developed a leaf disease detection model using VGG19, achieving high accuracy in classifying plant diseases for
improved agricultural monitoring.
•Neural Style Transfer
I implemented Arbitrary Neural Style Transfer using TensorFlow Hub’s model to apply diverse artistic styles to
images.
•Medical Transcript Classification
I developed a medical transcript classification system using N-grams and various supervised machine learning al-
gorithms, achieving accurate categorization of clinical texts.
SKILLS
•Hard Skills
Python, NLP , SQL, Text Analytics , Machine Learning and Deep Learning
•Soft Skills
Effective Communication , Adaptability , Attention to detail , Creative thinking , Team Collaboration
