=== RAW RESPONSE ===
Length: 1760
Source: Ka<PERSON><PERSON> salgar.pdf
Timestamp: 1750249580.8870869
=== CONTENT ===
```json
{
    "name": "<PERSON><PERSON><PERSON>",
    "email": "<EMAIL>",
    "phone": "+917410134201",
    "education": [
        {
            "degree": "BE - Computer Science & Engineering",
            "institution": "Sanjay Ghodawat Institute, Atigre",
            "year": "April- 2020"
        },
        {
            "degree": "Diploma - Computer Engineering",
            "institution": "SIT Polytechnic, Yadrav",
            "year": "April- 2017"
        },
        {
            "degree": "SSC",
            "institution": "Ratnadeep High School, Ganganagar",
            "year": "March- 2014"
        }
    ],
    "skills": [
        "C#",
        "ASP.NET Core MVC",
        "MSSQL",
        "Service Now"
    ],
    "experience": [
        {
            "company_name": "Cognizant",
            "role": "Jr. Software Engineer",
            "duration": "2021-2023",
            "key_responsibilities": "Worked on analyzing and updating scripts for applications.\nKnowledge in using Service Now tickets, and handled\ndeployments using Service Now Change.\nInvolved in the enhancement of applications using MVC\ndesign patterns.\nPrepared the documents which are required for the release.\nRequirement gathering, analysis, design, unit testing, and\ndeployment for enhancement of applications.\nWorked on Unit Test Cases preparation and execution."
        }
    ],
    "projects": [],
    "certifications": [
        "Microsoft - Azure Fundamentals"
    ],
    "domain_of_interest": [],
    "languages_known": [],
    "achievements": [
        "81.25%",
        "88.20%"
    ],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": null,
    "personal_projects": [],
    "social_media": []
}
```