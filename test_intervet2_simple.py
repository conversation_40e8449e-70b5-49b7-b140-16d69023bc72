#!/usr/bin/env python3
"""
Simple test to verify the new scoring system changes in calculate_candidate_job_fit function.
This test uses the /resume and /jd_parser endpoints to get data, then tests the scoring logic.
"""

import requests
import json
import os
import time

# Configuration
API_BASE_URL = "http://localhost:8000"
TEST_RESUME_PATH = "resumes for testing/Resume-Raman Lu<PERSON>ch.pdf"

def test_new_scoring_system():
    """Test the new scoring system by parsing a resume and creating a mock JD."""
    
    print("🧪 Testing New Scoring System")
    print("=" * 50)
    
    # Check if test files exist
    if not os.path.exists(TEST_RESUME_PATH):
        print(f"❌ Test resume not found: {TEST_RESUME_PATH}")
        return False
    
    print(f"📄 Using test resume: {TEST_RESUME_PATH}")
    
    try:
        # Step 1: Parse the resume using hybrid method
        print("\n🔍 Step 1: Parsing resume using /hybrid_resume endpoint...")
        
        with open(TEST_RESUME_PATH, 'rb') as resume_file:
            files = {'file': (os.path.basename(TEST_RESUME_PATH), resume_file, 'application/pdf')}
            
            start_time = time.time()
            response = requests.post(f"{API_BASE_URL}/hybrid_resume", files=files, timeout=120)
            end_time = time.time()
            
            print(f"⏱️  Resume parsing completed in {end_time - start_time:.2f} seconds")
            print(f"📊 Response status: {response.status_code}")
            
            if response.status_code != 200:
                print(f"❌ Resume parsing failed: {response.text}")
                return False
            
            resume_data = response.json()
            extraction_method = resume_data.get("extraction_method", "unknown")
            print(f"✅ Resume parsed successfully using: {extraction_method}")
            
            if "hybrid" in extraction_method.lower():
                print("✅ Hybrid resume parsing confirmed!")
            else:
                print("⚠️  Expected hybrid parsing, but got:", extraction_method)
        
        # Step 2: Create a mock JD for testing
        print("\n🔍 Step 2: Creating mock JD data for testing...")
        
        mock_jd_data = {
            "job_title": "Senior Software Engineer",
            "company_name": "TechCorp Solutions",
            "location": "Bangalore, India",
            "required_skills": [
                "Python", "JavaScript", "Java", "React", "Node.js", 
                "AWS", "SQL", "NoSQL", "Microservices", "CI/CD"
            ],
            "preferred_skills": [
                "Machine Learning", "Docker", "Kubernetes", "Data Science"
            ],
            "required_experience": "5+ years of experience in software development",
            "education_requirements": ["Bachelor's degree in Computer Science or related field"],
            "education_details": {
                "degree_level": "Bachelor's",
                "field_of_study": "Computer Science",
                "alternatives": "related field"
            }
        }
        
        print("✅ Mock JD data created")
        
        # Step 3: Test the intervet endpoint with new scoring
        print("\n🔍 Step 3: Testing candidate-job fit with new scoring system...")
        
        intervet_payload = {
            "resume_json": resume_data,
            "jd_json": mock_jd_data
        }
        
        start_time = time.time()
        response = requests.post(
            f"{API_BASE_URL}/intervet", 
            json=intervet_payload, 
            timeout=120,
            headers={"Content-Type": "application/json"}
        )
        end_time = time.time()
        
        print(f"⏱️  Scoring completed in {end_time - start_time:.2f} seconds")
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ Scoring failed: {response.text}")
            return False
        
        result = response.json()
        print("✅ Candidate-job fit analysis successful!")
        
        # Step 4: Analyze the new scoring system
        print("\n📊 Analyzing New Scoring System:")
        
        total_score = result.get("total_score", 0)
        fit_category = result.get("fit_category", "Unknown")
        detailed_scores = result.get("detailed_scores", {})
        detailed_rationale = result.get("detailed_rationale", {})
        
        print(f"🎯 Total Score: {total_score}/100")
        print(f"📈 Fit Category: {fit_category}")
        
        print(f"\n📋 Detailed Scores (New Weightage System):")
        
        # Expected categories with their max scores
        expected_categories = {
            "skills_match_direct": 35,      # 35% weight
            "experience_match": 35,         # 35% weight  
            "reliability": 15,              # 15% weight
            "certifications": 5,            # 5% weight
            "location_match": 5,            # 5% weight
            "alma_mater": 5                 # 5% weight
        }
        
        total_expected = sum(expected_categories.values())
        print(f"📊 Expected total max score: {total_expected}")
        
        all_categories_present = True
        for category, max_score in expected_categories.items():
            actual_score = detailed_scores.get(category, 0)
            percentage = (actual_score / max_score) * 100 if max_score > 0 else 0
            rationale = detailed_rationale.get(category, "No rationale provided")
            
            print(f"   • {category.replace('_', ' ').title()}: {actual_score}/{max_score} ({percentage:.1f}%)")
            print(f"     Rationale: {rationale}")
            
            if category not in detailed_scores:
                all_categories_present = False
                print(f"     ❌ Missing category: {category}")
        
        # Check that removed categories are NOT present
        removed_categories = ["skills_match_subjective", "academic_match"]
        removed_found = []
        
        for category in removed_categories:
            if category in detailed_scores:
                removed_found.append(category)
        
        print(f"\n✅ Verification Results:")
        print(f"   • All expected categories present: {'✅ Yes' if all_categories_present else '❌ No'}")
        print(f"   • Removed categories absent: {'✅ Yes' if not removed_found else '❌ No - Found: ' + ', '.join(removed_found)}")
        print(f"   • Total score calculation: {'✅ Correct' if total_score == sum(detailed_scores.values()) else '❌ Incorrect'}")
        print(f"   • Max possible score: {total_expected} points")
        
        # Summary
        print(f"\n📊 Scoring System Summary:")
        print(f"   • Skills Match (Direct): {detailed_scores.get('skills_match_direct', 0)}/35 (35%)")
        print(f"   • Experience Match: {detailed_scores.get('experience_match', 0)}/35 (35%)")
        print(f"   • Reliability: {detailed_scores.get('reliability', 0)}/15 (15%)")
        print(f"   • Certifications: {detailed_scores.get('certifications', 0)}/5 (5%)")
        print(f"   • Location Match: {detailed_scores.get('location_match', 0)}/5 (5%)")
        print(f"   • Alma Mater: {detailed_scores.get('alma_mater', 0)}/5 (5%)")
        print(f"   • TOTAL: {total_score}/100")
        
        return all_categories_present and not removed_found
        
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to API. Make sure the server is running on localhost:8000")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def check_api_health():
    """Check if the API is running and healthy."""
    
    try:
        response = requests.get(f"{API_BASE_URL}/", timeout=10)
        if response.status_code == 200:
            print("✅ API is running and healthy")
            return True
        else:
            print(f"❌ API health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Could not reach API: {e}")
        return False

def main():
    """Main test function."""
    
    print("🧪 New Scoring System Test")
    print("=" * 50)
    print("Testing:")
    print("1. ✅ Hybrid resume parsing")
    print("2. ✅ New 6-category scoring system")
    print("3. ✅ Removal of subjective skills and academic match")
    print("4. ✅ Correct weightage distribution")
    print("=" * 50)
    
    # Check API health
    if not check_api_health():
        print("\n💡 Make sure to start the API server first:")
        print("   python main.py")
        return
    
    # Test new scoring system
    success = test_new_scoring_system()
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"✅ New scoring system: {'PASSED' if success else 'FAILED'}")
    
    if success:
        print("\n🎉 All tests passed! New scoring system is working correctly.")
        print("\n📋 Changes Verified:")
        print("   ✅ Hybrid resume parsing working")
        print("   ✅ 6-category scoring system active")
        print("   ✅ Skills: 35%, Experience: 35%, Reliability: 15%")
        print("   ✅ Certifications: 5%, Location: 5%, Alma Mater: 5%")
        print("   ✅ Subjective skills matching removed")
        print("   ✅ Academic match scoring removed")
        print("   ✅ Total scoring out of 100 points")
    else:
        print("\n❌ Some tests failed. Check the error messages above.")

if __name__ == "__main__":
    main()
