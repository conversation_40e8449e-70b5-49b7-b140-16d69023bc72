{"event": "session_start", "session_id": "ce9d0ddd-f25d-4c98-9365-570d9e6e2c18", "timestamp": "2025-06-26T18:19:00.392217", "message": "New API session started"}
{"event": "request_start", "session_id": "ce9d0ddd-f25d-4c98-9365-570d9e6e2c18", "request_id": "5ac88bee-a8ef-43b8-9f90-848ff1460ac8", "endpoint": "/", "timestamp": "2025-06-26T18:19:01.176672", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "ce9d0ddd-f25d-4c98-9365-570d9e6e2c18", "request_id": "5ac88bee-a8ef-43b8-9f90-848ff1460ac8", "endpoint": "/", "timestamp": "2025-06-26T18:19:01.178855", "total_time_seconds": 0.0021834373474121094, "status_code": 200, "message": "Request completed in 0.0022s with status 200"}
{"event": "request_start", "session_id": "ce9d0ddd-f25d-4c98-9365-570d9e6e2c18", "request_id": "1d6d67a3-ed01-4c28-a94d-afc3d307a4f7", "endpoint": "/favicon.ico", "timestamp": "2025-06-26T18:19:01.262989", "message": "Request started for endpoint: /favicon.ico"}
{"event": "request_complete", "session_id": "ce9d0ddd-f25d-4c98-9365-570d9e6e2c18", "request_id": "1d6d67a3-ed01-4c28-a94d-afc3d307a4f7", "endpoint": "/favicon.ico", "timestamp": "2025-06-26T18:19:01.263989", "total_time_seconds": 0.0009999275207519531, "status_code": 404, "message": "Request completed in 0.0010s with status 404"}
{"event": "request_start", "session_id": "ce9d0ddd-f25d-4c98-9365-570d9e6e2c18", "request_id": "4115b130-35b1-4af8-8b1a-a2edd4997e18", "endpoint": "/docs", "timestamp": "2025-06-26T18:19:03.740240", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "ce9d0ddd-f25d-4c98-9365-570d9e6e2c18", "request_id": "4115b130-35b1-4af8-8b1a-a2edd4997e18", "endpoint": "/docs", "timestamp": "2025-06-26T18:19:03.741239", "total_time_seconds": 0.00099945068359375, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "ce9d0ddd-f25d-4c98-9365-570d9e6e2c18", "request_id": "6e1d2a17-60be-475d-bc61-e46f3633c84f", "endpoint": "/openapi.json", "timestamp": "2025-06-26T18:19:03.847581", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "ce9d0ddd-f25d-4c98-9365-570d9e6e2c18", "request_id": "6e1d2a17-60be-475d-bc61-e46f3633c84f", "endpoint": "/openapi.json", "timestamp": "2025-06-26T18:19:03.879592", "total_time_seconds": 0.03201031684875488, "status_code": 200, "message": "Request completed in 0.0320s with status 200"}
