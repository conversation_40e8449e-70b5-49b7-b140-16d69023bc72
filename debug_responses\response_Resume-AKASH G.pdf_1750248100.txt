=== RAW RESPONSE ===
Length: 3942
Source: Resume-AKASH G.pdf
Timestamp: 1750248100.7292056
=== CONTENT ===
```json
{
    "name": "Akash G",
    "email": null,
    "phone": null,
    "education": [
        {
            "degree": "Bachelor of Technology (Artiﬁcial intelligence)",
            "institution": "Newton School of Technology, Rishihood University",
            "year": "2023 - 2027"
        },
        {
            "degree": "Intermediate",
            "institution": "Kendriya Vidyalaya",
            "year": "2022 - 2023"
        },
        {
            "degree": "Matriculation",
            "institution": "Kendriya Vidyalaya",
            "year": "2020 - 2021"
        }
    ],
    "skills": [
        "SQL",
        "Java",
        "Python",
        "JavaScript",
        "CSS",
        "HTML",
        "React",
        "Express JS",
        "Prisma ORM",
        "MySQL",
        "Tailwind",
        "NodeJS",
        "Data Structure"
    ],
    "experience": [],
    "projects": [
        {
            "name": "Fashion Cart E-Commerce",
            "description": "Built a full-stack e-commerce application with React for frontend, Node.js and Express for backend, Prisma and MySQL for database management, and Stripe for secure payment processing. User-friendly product search, category-based browsing, secure Sign In/Sign Out ,Add-to-cart and checkout functionality with integrated Stripe payment processing, Responsive design with Tailwind CSS",
            "key_responsibilities": "Built a full-stack e-commerce application with React for frontend, Node.js and Express for backend, Prisma and MySQL for database management, and Stripe for secure payment processing. User-friendly product search, category-based browsing, secure Sign In/Sign Out ,Add-to-cart and checkout functionality with integrated Stripe payment processing, Responsive design with Tailwind CSS"
        },
        {
            "name": "Chat Application - Talk Stream",
            "description": "Developed a dynamic real-time chat application with Firebase for backend integration, providing smooth communication, user authentication, and robust state management. Real-time messaging with image sharing, user search, and login/logout functionality. Chat list management for personalized interactions.",
            "key_responsibilities": "Developed a dynamic real-time chat application with Firebase for backend integration, providing smooth communication, user authentication, and robust state management. Real-time messaging with image sharing, user search, and login/logout functionality. Chat list management for personalized interactions."
        },
        {
            "name": "Apple Music Clone",
            "description": "Developed a responsive Apple website clone, replicating design aesthetics, interactive elements, and layout to provide an authentic user experience. Homepage, product pages, and navigation bar designed to emulate Apple's clean and minimalistic interface.",
            "key_responsibilities": "Developed a responsive Apple website clone, replicating design aesthetics, interactive elements, and layout to provide an authentic user experience. Homepage, product pages, and navigation bar designed to emulate Apple's clean and minimalistic interface."
        }
    ],
    "certifications": [],
    "domain_of_interest": [],
    "languages_known": [],
    "achievements": [
        "Solved over 300 problems on Leetcode",
        "Secured 3rd position in the college marathon"
    ],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": "Aspiring software developer with a strong foundation in full-stack development and a passion for solving real-world problems. Proficient in HTML, CSS, JavaScript, React.js, Node.js, Express.js, MySQL, and SQL, with a solid background in Java, OOP principles, and DSA. Accomplished problem-solver with over 300 challenges completed on LeetCode.",
    "personal_projects": [],
    "social_media": [
        "null",
        "null"
    ]
}
```