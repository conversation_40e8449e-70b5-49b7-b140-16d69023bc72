=== RAW RESPONSE ===
Length: 3283
Source: <PERSON><PERSON><PERSON> (1).pdf
Timestamp: 1750249359.6015463
=== CONTENT ===
```json
{
    "name": "<PERSON><PERSON><PERSON>",
    "email": "<EMAIL>",
    "phone": "+91 8381010425",
    "education": [
        {
            "degree": "M. Tech, Software Engineering",
            "institution": "Veermata Jijabai Technological Institute, Mumbai",
            "year": "Feb 2021 - Oct 2022"
        },
        {
            "degree": "B.E, Computer Engineering",
            "institution": "Maharashtra Institute of Technology, Pune",
            "year": "2016 – 2020"
        }
    ],
    "skills": [
        "AWS",
        "Docker",
        "Jenkins",
        "GIT",
        "Ansible",
        "Maven",
        "ASP.NET",
        "C#",
        "VB .NET",
        "MYSQL",
        "HTML",
        "MVC",
        "Windows 10"
    ],
    "experience": [
        {
            "company_name": "National Securities Depository Limited (PC Center)",
            "role": "Junior Software Engineer",
            "duration": "JAN 2023 - present",
            "key_responsibilities": "Used vb.net for coding. Used Itextsharp to generate reports. Involved in documentation of the components and reporting."
        }
    ],
    "projects": [
        {
            "name": "Depository Participant Module DPM+",
            "description": "DPM+ is a web application which is used to store the details of Depository participants (DPs) and their account details in various modules of DPM+. It also consists of the PAN details of DPs. CI/CD pipeline implementation using Jenkins. Implemented CI/CD pipeline using AWS. Responsible for Docker container creation and management, Docker file management, deployment of micro services to container. Cloud infrastructure provision and management, securing cloud environment by implementing best practices in security and network domain. Responsible for server configuration management via Ansible and environments management. Have conducted internal trainings on DevOps tools like Docker and AWS. Troubleshooting in build environment specially fixing development issues, setup git branch strategy, merge feature branches with master and push changes to Github based source code repo sitory. Managing build and release definition as per project requirement, trigger build job. Created Jenkins file for automated continuous integration and deployment. Responsible for Production deployment pipeline by defining zero down DevOps strategy. Responsible for managing AWS cloud-based resources such as EC2, VPC, S3, Cloud formation etc. Responsible for high availability of running applications on cloud instances and containers. Working with Developers, Release management and tool decision team to enhance CI/CD pipeline. Implemented best practices for Docker, Ansible, Gitlab, Jenkins, AWS,pipelineetc."
        }
    ],
    "certifications": [
        "AWS Certified Cloud Practitioner",
        "Aws re/start graduate"
    ],
    "domain_of_interest": [
        "Dot Net",
        "SQL",
        "DevOps",
        "AWS"
    ],
    "languages_known": [
        "English"
    ],
    "achievements": [
        "Solving development issues",
        "Implementing CI/CD pipeline"
    ],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": null,
    "personal_projects": [],
    "social_media": []
}
```