=== RAW RESPONSE ===
Length: 3700
Source: Resume-MEESALA SREE SAI NATH.pdf
Timestamp: 1750246321.6684992
=== CONTENT ===
```json
{
    "name": "Meesala Sr<PERSON>",
    "email": null,
    "phone": null,
    "education": [
        {
            "degree": "Bachelor of Technology (Computer Science)",
            "institution": "Newton School of Technology, Rishihood University",
            "year": "2023 - 2027"
        },
        {
            "degree": "Intermediate (Class XII)",
            "institution": "Sri Chaitanya Junior College",
            "year": "2020 - 2022"
        },
        {
            "degree": "Matriculation (Class X)",
            "institution": "Kendriya Vidyalaya No-1, Air Force Academy",
            "year": "2019 - 2020"
        }
    ],
    "skills": [
        "Java",
        "JavaScript",
        "CSS",
        "HTML",
        "Python",
        "React",
        "Mongo DB",
        "Django",
        "Express JS",
        "NodeJS",
        "Prisma ORM",
        "MySQL",
        "Tailwind",
        "Hadoop HDFS",
        "Data Structure"
    ],
    "experience": [
        {
            "company_name": "Spectacom Global, Gurugram, Haryana",
            "role": "SDE INTERN",
            "duration": "January 2025 - Present",
            "key_responsibilities": "Developed a leaderboard system in Django, supporting regional and individual rankings. Integrated detailed participant timing records with numerical insights for performance tracking. Optimized Django ORM queries for efficient leaderboard updates and retrieval."
        }
    ],
    "projects": [
        {
            "name": "Laundry Pro",
            "description": "Laundry Pro simplifies laundry services with an efficient platform for students to place and track orders and admins to manage operations. Features: Secure login for students and admins. Admin tools for order status updates and record management."
        },
        {
            "name": "Fabrix",
            "description": "Developed a dynamic, simulated e-commerce platform with secure user authentication and product browsing using dummy data for a modern, responsive shopping experience. Features: Secure user authentication with personalized features. Product browsing and filtering using dummy data, Shopping cart management with Razorpay integration for secure checkout."
        },
        {
            "name": "Crypto - manager",
            "description": "Created a web application for managing and tracking cryptocurrency investments with real-time data and a responsive, interactive user interface. Features: Real-time API integration to fetch live cryptocurrency data and market trends. Simulated trading for buying, selling, and holding cryptocurrencies in a risk-free environment."
        }
    ],
    "certifications": [
        "Python for Beginners - Newton School (2024)",
        "Reliance Foundation Undergraduate Scholar (Merit-based)"
    ],
    "domain_of_interest": [
        "Data Structure"
    ],
    "languages_known": [
        "English"
    ],
    "achievements": [
        "Ranked in the top-3 in the Stealth Fire Hackathon conducted by Newton School of Technology.",
        "Earned the Rajya Puraskar award in Scouts and Guides after completing rigorous training and Community service.",
        "Qualified for the Semi-Finals inBadminton at the University Sports Fest"
    ],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": "Full Stack Developer skilled in React, Tailwind CSS, Node.js, Express, Django, and MySQL, with experience in scalable APIs and dynamic UIs. Passionate about learning and building high-performing web applications.",
    "personal_projects": [],
    "social_media": [
        "https://github.com",
        "https://github.com"
    ]
}
```