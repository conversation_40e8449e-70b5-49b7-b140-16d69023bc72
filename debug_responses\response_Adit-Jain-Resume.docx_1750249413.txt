=== RAW RESPONSE ===
Length: 4210
Source: Adit-Jain-Resume.docx
Timestamp: 1750249413.2914538
=== CONTENT ===
```json
{
    "name": "ADIT JAIN",
    "email": null,
    "phone": "+91 8095011293",
    "education": [
        {
            "degree": "Bachelor of Computer Science",
            "institution": "Washington State University, USA",
            "year": "December 2021"
        }
    ],
    "skills": [
        "ASP.NET",
        ".NET framework",
        "REST API",
        "API Development",
        "Node.js",
        "Docker",
        "CI/CD",
        "SQL",
        "MSSQL",
        "MongoDB",
        "HTML",
        "CSS",
        "JavaScript",
        "React.js",
        "JQuery",
        "Kendo UI",
        "C#",
        "Java",
        "Python",
        "C++",
        "Object oriented programming(OOPS)",
        "SOLID Principles",
        "Git/Github"
    ],
    "experience": [
        {
            "company_name": "NOVAAUTOMATA INNOVATIONS PVT. LTD.",
            "role": "Full stack web developer",
            "duration": "April 2024 - Present",
            "key_responsibilities": "Full stack web developer using REST APIs in ASP.NET. Database creation in SQL. Software Engineer\nTEOCO\nVirginia, USA\nFull stack web application that did in depth data analysis and reporting using Kendo UI, REST APIs in ASP.NET.\nCreated several SQL queries for efficient data retrieval.\nIT Software Engineer\nJan 2022 - March 2023\nMicron Technology\nBoise, ID, USA\nDeveloped several web applications that supports company’s memory and storage solutions in collaborative environment using ASP.NET and .Net framework.\nEnhanced testing efficiency through the implementation of an automation solution, resulting in a substantial 30% reduction in test suite execution time.\nManaged local Docker deployments and created local clusters\nResolved several bugs through meticulous log tracing and use of POSTMAN requests.\nFacilitated coordination with cross-functional teams spanning across North America and Asia, ensuring seamless deployment of several software releases.\nCreated and enhanced SQL Store procedures."
        },
        {
            "company_name": "TEOCO",
            "role": "Software Engineer",
            "duration": "May 2023 - Sept 2023",
            "key_responsibilities": "Virginia, USA\nFull stack web application that did in depth data analysis and reporting using Kendo UI, REST APIs in ASP.NET.\nCreated several SQL queries for efficient data retrieval.\nIT Software Engineer\nJan 2022 - March 2023\nMicron Technology\nBoise, ID, USA\nDeveloped several web applications that supports company’s memory and storage solutions in collaborative environment using ASP.NET and .Net framework.\nEnhanced testing efficiency through the implementation of an automation solution, resulting in a substantial 30% reduction in test suite execution time.\nManaged local Docker deployments and created local clusters\nResolved several bugs through meticulous log tracing and use of POSTMAN requests.\nFacilitated coordination with cross-functional teams spanning across North America and Asia, ensuring seamless deployment of several software releases.\nCreated and enhanced SQL Store procedures."
        },
        {
            "company_name": "Micron Technology",
            "role": "Software Engineer intern",
            "duration": "June 2021 - Sept 2021",
            "key_responsibilities": "Boise, ID, USA\nContributed to a startup during its formative stages.\nEngineered backend services by leveraging several APIs, including the Stripe API, Facebook Graph API.\nCreated several REST API’s using C# and ASP.NET."
        }
    ],
    "projects": [
        {
            "name": "Airport of the future - Alaska Airlines",
            "description": "Created a web system to register user complaints for Alaska Airlines in a team of 5, developed a web system from scratch using React, Node, MongoDB, Azure to digitalize an existing paper form to decrease human error and increase submission efficiently by 30%. System deployed to Microsoft Azure and in use by Alaska Airlines CRO’s (Complaint Resolution officer) across United States."
        },
        {
            "name": "Calories Tracker Application",
            "description": "Build a project that track calories consumed