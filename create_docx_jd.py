#!/usr/bin/env python3
"""
Create a DOCX JD file for testing intervet2 endpoint.
"""

try:
    from docx import Document
    
    def create_docx_jd():
        """Create a DOCX JD file."""
        
        # Read the text content
        with open('test_jd.txt', 'r') as f:
            content = f.read()
        
        # Create a new document
        doc = Document()
        
        # Add content line by line
        for line in content.split('\n'):
            if line.strip():
                if line.isupper() and not line.startswith('•'):
                    # Make headers bold
                    p = doc.add_paragraph()
                    run = p.add_run(line)
                    run.bold = True
                else:
                    doc.add_paragraph(line)
            else:
                doc.add_paragraph()  # Empty line
        
        # Save the document
        filename = "test_jd.docx"
        doc.save(filename)
        print(f"✅ Created DOCX JD file: {filename}")
        return filename

    if __name__ == "__main__":
        create_docx_jd()

except ImportError:
    print("❌ python-docx not installed. Using alternative method...")
    
    # Alternative: Just copy the text file with .docx extension for testing
    import shutil
    shutil.copy('test_jd.txt', 'test_jd.docx')
    print("✅ Created test_jd.docx (text file with .docx extension)")
