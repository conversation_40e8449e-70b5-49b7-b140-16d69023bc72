=== RAW RESPONSE ===
Length: 2990
Source: <PERSON><PERSON><PERSON>_Wani_Resume.pdf
Timestamp: 1750249559.178395
=== CONTENT ===
```json
{
    "name": "CHANDRAKANT WANI",
    "email": "<EMAIL>",
    "phone": "+91 7066537017",
    "education": [
        {
            "degree": "Master of Computer Applications",
            "institution": "KBC North Maharashtra University, Jalgaon",
            "year": "2022"
        }
    ],
    "skills": [
        "C#",
        "ASP.NET Core",
        "English",
        "Marathi",
        "Hindi",
        "HTML",
        "CSS",
        "React JS",
        "SQL",
        "ASP.NET",
        "Web API",
        "SQL Server 2008",
        "Entity Framework",
        ".NET",
        "Git",
        "GitHub",
        "Pivotal Tracker",
        "IIS"
    ],
    "experience": [
        {
            "company_name": "V2Solutions, Navi Mumbai",
            "role": "Associate Software Engineer",
            "duration": "July 2022",
            "key_responsibilities": "Designed and implemented various controllers and API components in C# and ASP.NET Web API models.\nCollaborated with team members to ensure seamless integration of existing systems and provisioned user interfaces for project metrics inputs.\nOptimized data retrieval and manipulation processes within the Web API.\nCreated comprehensive dashboards for senior management decision-making."
        }
    ],
    "projects": [
        {
            "name": "Digital ASP",
            "description": "Environment :  C#, ASP.NET, Web API, SQL Server 2008, Entity Framework, React JS\nResponsibilities : Designed and implemented various controllers and API components in C# and ASP.NET Web API models.\nCollaborated with team members to ensure seamless integration of existing systems and provisioned user interfaces for project metrics inputs.\nOptimized data retrieval and manipulation processes within the Web API.\nCreated comprehensive dashboards for senior management decision-making."
        },
        {
            "name": "Vibrant Web Refresh",
            "description": "Environment :  C#, .NET, SQL, Git, GitHub, Pivotal Tracker, IIS\nResponsibilities : Conducted analysis and development of the timesheet module.\nCompleted the development of reports as per project requirements.\nStreamlined data collection from multiple sources and optimized report generation processes .\nUtilized version control tools like Git and GitHub for code management and collaboration.\nActively engaged in project tracking and task management using Pivotal Tracker."
        }
    ],
    "certifications": [
        "ASP .Net Core: Big Picture from Pluralsight.",
        "ASP .Net Core Web API Fundamentals from Pluralsight."
    ],
    "domain_of_interest": [],
    "languages_known": [
        "English",
        "Marathi",
        "Hindi"
    ],
    "achievements": [
        "Percentage: 91.8 8"
    ],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": null,
    "personal_projects": [],
    "social_media": [
        "linkedin.com/in/chandrakantwani"
    ]
}
```