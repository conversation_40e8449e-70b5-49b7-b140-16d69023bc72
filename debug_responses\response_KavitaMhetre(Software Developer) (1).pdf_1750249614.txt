=== RAW RESPONSE ===
Length: 4149
Source: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(Software Developer) (1).pdf
Timestamp: 1750249614.708125
=== CONTENT ===
```json
{
    "name": "<PERSON><PERSON><PERSON>he<PERSON>",
    "email": "<EMAIL>",
    "phone": "+91 8668842698",
    "education": [
        {
            "degree": "Post Graduate Diploma in Advance Computing",
            "institution": "CDAC Bangalore",
            "year": "2024"
        },
        {
            "degree": "Bachelor of Engineering",
            "institution": "Solapur University",
            "year": "Distinction"
        },
        {
            "degree": "H.S.C",
            "institution": "Walchand College of Arts & Science, Solapur",
            "year": "Distinction"
        },
        {
            "degree": "S.S.C",
            "institution": "Pune Board",
            "year": "Distinction"
        }
    ],
    "skills": [
        "Reactjs",
        "Bootstrap",
        "HTML",
        "CSS",
        "Java",
        "Spring Boot",
        "MySQL",
        "REST API",
        "Microservice",
        "Git",
        "Eclipse IDE",
        "STS IDE",
        "Visual Studio Code",
        "Postman",
        "GitHub",
        "Maven",
        "OOPs",
        "Agile",
        "SDLC",
        "Teamwork",
        "Problem Solving",
        "Initiative",
        "Flexible",
        "Quick Learner"
    ],
    "experience": [
        {
            "company_name": "Rego Digital Solution Pvt Ltd",
            "role": "Frontend Developer",
            "duration": "Jan 2022 to Current",
            "key_responsibilities": "Collaborate with cross functional teams to gather and define project requirements. Design and develop scalable, robust and secure web application using Reactjs Framework. Implement efficient and reusable front -end and backend system. Attended Project me etings and interacted with other team members in order to resolve the problems."
        },
        {
            "company_name": "Rego Digital Solution Pvt Ltd",
            "role": "Frontend Developer",
            "duration": "Jan 2022 to Current",
            "key_responsibilities": "Collaborate with cross functional teams to gather and define project requirements. Design and develop scalable, robust and secure web application using Reactjs Framework. Implement efficient and reusable front -end and backend system. Attended Project me etings and interacted with other team members in order to resolve the problems."
        },
        {
            "company_name": "Rego Digital Solution Pvt Ltd",
            "role": "Backend Developer",
            "duration": "Jan 2022 to Current",
            "key_responsibilities": "REST Microservices based API development for Mobile banking services. Collaborate with cross functional teams, including product managers and designers, to analyze customer needs and design tailored software solutions. Tech Stack:  Java, Spring Boot, Postman, MySQL, Microservice."
        }
    ],
    "projects": [
        {
            "name": "Uflex Technology Web app Design and Develop",
            "description": "Uflex Technology is focusing on innovation to create differentiation, proximity to customers and end -to-end flexible packaging solutions."
        },
        {
            "name": "Mangalam Web app Design",
            "description": "Mangalam is a startup event planning website facilitating event orders across India."
        },
        {
            "name": "CDAC Project – Design and Develop Website for Event Planner",
            "description": "Utsava:  Event planner is a web -based project.  This project allows possible event audiences to register online with the use of our website and also the possible clients to inquire about the customers desired venue in which they possibly place their event."
        }
    ],
    "certifications": [
        "Hacker Rank Certificate : Java (Basics), SQL ( Basics), SQL (Intermediate), SQL (Adv )",
        "Accenture Certificate: Developer & Technology Job Simulation - Agile, Waterfall, SDLC, STLC",
        "PreCAT Certificate : It includes OS, OOPs , DSA, Computer Fundamentals Networking"
    ],
    "domain_of_interest": [],
    "languages_known": [
        "English",
        "Hindi",
        "Marathi",
        "Telugu"
