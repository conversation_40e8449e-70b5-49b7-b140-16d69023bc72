=== RAW RESPONSE ===
Length: 3436
Source: Resume-<PERSON><PERSON> jain.pdf
Timestamp: 1750248272.1538594
=== CONTENT ===
```json
{
    "name": "<PERSON><PERSON>",
    "email": null,
    "phone": null,
    "education": [
        {
            "degree": "Bachelor of Technology (Artiﬁcial intelligence)",
            "institution": "Newton School of Technology, Rishihood University",
            "year": "2023 - 2027"
        },
        {
            "degree": "Intermediate (Class XII)",
            "institution": "Growell School (CBSE)",
            "year": "2022 - 2023"
        },
        {
            "degree": "Matriculation (Class X)",
            "institution": "Growell School (CBSE)",
            "year": "2021 - 2022"
        }
    ],
    "skills": [
        "TypeScript",
        "Java",
        "Python",
        "JavaScript",
        "CSS",
        "HTML",
        "React",
        "MySQL",
        "Express JS",
        "Prisma ORM",
        "Tailwind",
        "Bootstrap",
        "NodeJS"
    ],
    "experience": [
        {
            "company_name": "Google",
            "role": "CONTRIBUTOR",
            "duration": "May 2024 - September 2024",
            "key_responsibilities": "Merged three critical pull requests to standardize UI elements across JupyterLab: Replaced all search inputs with the toolkit’s standardized search input. Applying a consistent tree view across table of contents, debugger, and running tabs. Integrated a consistent icon component into the toolkit. Enhanced settings editor with advanced widgets (CustomCheckboxWidget, SelectWidget)."
        }
    ],
    "projects": [
        {
            "name": "3D Art Gallery",
            "description": "3D Art Gallery is a full-stack e-commerce platform allowing users to explore and purchase artwork in a 3D virtual space. Artists can easily upload and manage their art for direct sale. Features: 3D Gallery Exploration: Realistic, interactive 3D browsing for an immersive experience. E-commerce Functions: Add-to-cart, direct purchasing. Artist Management: Easy upload and management of artwork for artists."
        },
        {
            "name": "MyPortfolio",
            "description": "Explore my portfolio to uncover my skills, experiences, and projects that highlight my capabilities and passion for innovation. Features: Built responsive navigation with a hamburger menu, smooth scroll, and clickable social links. Enhanced UX with a modern design, clean layout, and optimised asset loading."
        }
    ],
    "certifications": [
        "GSOC’24 COMPLETION CERTIFICATE"
    ],
    "domain_of_interest": [
        "Artificial Intelligence"
    ],
    "languages_known": [
        "English"
    ],
    "achievements": [
        "Won the StealthFire Hackathon with CampusLink, an innovative project connecting aspirants and college students.",
        "Participated in the Level 1 E-Commerce Tech Quiz of the Flipkart GRiD 6.0 - Software Development Track organised by the Flipkart ."
    ],
    "publications": [],
    "volunteer_experience": [
        "Participated in a university-organized 100km walkathon, showcasing endurance and commitment to campus activities."
    ],
    "references": [],
    "summary": "Dynamic and self-motivated individual with a strong foundation in HTML, CSS, JavaScript, React.js, Node.js, Express.js, and MySQL. Eager to leverage skills and collaborate within a professional team environment to drive impactful results.",
    "personal_projects": [],
    "social_media": [
        "null",
        "null"
    ]
}
```