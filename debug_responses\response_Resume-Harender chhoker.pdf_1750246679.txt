=== RAW RESPONSE ===
Length: 3018
Source: Resume-<PERSON>nder chhoker.pdf
Timestamp: 1750246679.662082
=== CONTENT ===
```json
{
    "name": "<PERSON><PERSON> Chhoker",
    "email": null,
    "phone": null,
    "education": [
        {
            "degree": "Bachelor of Technology (Artiﬁcial intelligence)",
            "institution": "Newton School of Technology, Rishihood University",
            "year": "2024 - 2028"
        },
        {
            "degree": "Intermediate (Class XII)",
            "institution": "St lukes sr sec school",
            "year": "2023 - 2024"
        },
        {
            "degree": "Matriculation (Class X)",
            "institution": "RD public school",
            "year": "2021 - 2022"
        }
    ],
    "skills": [
        "Python",
        "JavaScript",
        "CSS",
        "HTML",
        "Figma",
        "Excel",
        "Data Structure",
        "Communication Skills",
        "Teamwork",
        "Creativity",
        "Time management",
        "Leadership",
        "Written communication",
        "Social Media"
    ],
    "experience": [],
    "projects": [
        {
            "name": "Age Calculator",
            "description": "An age calculator web application that uses HTML, CSS, and JavaScript to calculate and display a user’s age based on their birthdate.\nFeatures include input for birthdate, automatic age calculation, real-time display of age in years, months, and days, and a user-friendly interface."
        },
        {
            "name": "Calculator",
            "description": "A simple web-based calculator built with HTML, CSS, and JavaScript that performs basic arithmetic operations like addition, subtraction, multiplication, and division.\nFeatures include a simple user interface, support for basic arithmetic operations, and clear/reset functionality for easy use."
        },
        {
            "name": "Stopwatch",
            "description": "Built a dynamic and interactive stopwatch application using React, featuring start, stop, reset, and time display functionality.\nFeatures include start, stop, and reset buttons, with a real-time display of elapsed time."
        },
        {
            "name": "Dive-into-creativity",
            "description": "Converted a Figma design into a fully responsive prototype using HTML and CSS, replicating the visual and interactive elements.\nFeatures a clean, responsive layout with interactive elements, ensuring seamless user experience across different devices."
        }
    ],
    "certifications": [
        "Python for Beginners - Newton School (2024)"
    ],
    "domain_of_interest": [],
    "languages_known": [],
    "achievements": [
        "Volleyball"
    ],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": "Passionate developer skilled in HTML, CSS, JavaScript, React.js, and Python. Strong problem-solving and algorithmic thinking, with 40+ LeetCode problems solved and a 600+ CodeForces rating",
    "personal_projects": [],
    "social_media": [
        "LinkedIn",
        "Github",
        "CodeChef",
        "Leetcode"
    ]
}
```