#!/usr/bin/env python3
"""
Test script to check if main.py can be imported without errors.
"""

import sys
import traceback

def test_main_import():
    """Test importing main.py to check for syntax or import errors."""
    
    print("🧪 Testing main.py import...")
    
    try:
        # Try to import main.py
        import main
        print("✅ main.py imported successfully")
        
        # Check if Opik is enabled
        if hasattr(main, 'opik_enabled'):
            print(f"📊 Opik enabled: {main.opik_enabled}")
        
        # Check if the app is created
        if hasattr(main, 'app'):
            print("✅ FastAPI app created successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to import main.py: {e}")
        print("\n🔍 Full traceback:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_main_import()
    if success:
        print("\n🎉 main.py imports successfully!")
        print("💡 You can now start the server with: python main.py")
    else:
        print("\n❌ There are issues with main.py that need to be fixed.")
