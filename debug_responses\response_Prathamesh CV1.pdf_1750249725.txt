=== RAW RESPONSE ===
Length: 2773
Source: Prathamesh CV1.pdf
Timestamp: 1750249725.1523566
=== CONTENT ===
```json
{
    "name": "<PERSON><PERSON><PERSON><PERSON>",
    "email": "<EMAIL>",
    "phone": "+91 9370162768",
    "education": [
        {
            "degree": "Bachelor of Engineering in Computer Science",
            "institution": "SPPU University",
            "year": "2019 - 2022"
        }
    ],
    "skills": [
        "ASP .NET Core MVC",
        "Dependency Injection",
        "REST",
        "Web API",
        "React JS",
        "LINQ",
        "Agile Methodologies",
        "MySQL",
        "Apex",
        "LWC",
        "Java",
        "Adobe Photoshop",
        "Corel",
        "Blender",
        "Filmora"
    ],
    "experience": [
        {
            "company_name": "Stacknexus, inc.",
            "role": "Technology Analyst",
            "duration": "03/2022 - 10/2023",
            "key_responsibilities": "Proficient in the core technologies of web development, I have applied my skills to create engaging and responsive user interfaces, ensuring a seamless and enjoyable user experience. Reduced data latency by 30%, enabling timely decision-making."
        },
        {
            "company_name": "Source Kaizen Pvt. Ltd",
            "role": "Frontend Developer Intern",
            "duration": "09/2021 - 02/2022",
            "key_responsibilities": "Proficient in the core technologies of web development, I have applied my skills to create engaging and responsive user interfaces, ensuring a seamless and enjoyable user experience. Reduced data latency by 30%, enabling timely decision-making."
        }
    ],
    "projects": [
        {
            "name": "Financial Accounting foundation",
            "description": "This application provides the help to provide/explore the financial domain. Architected ReactJS-based UI with responsive design principles. Experience in .Net Core MVC, Web API, REST, DI, LINQ. Experience in SFDC Development implementing the Workflow, Apex, SOQL, LWC, VF, Aura."
        }
    ],
    "certifications": [
        "Object Oriented Data Structures in CPP by Coursera",
        "ASP.NET Core 8 by Web University of Harsha Vardhan",
        "Foundations of User Experience Design Authorized by Google offered by Coursera Vidya Pratishthan’s Kamalnayan Bajaj Institute of Engineering and Technology, Baramati"
    ],
    "domain_of_interest": [
        "Artificial Intelligence and Machine Learning"
    ],
    "languages_known": [
        "English",
        "Marathi"
    ],
    "achievements": [
        "Reduced data latency by 30%, enabling timely decision-making"
    ],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": null,
    "personal_projects": [],
    "social_media": [
        "linkedin.com/in/prathamesh-pawar-2785841a1"
    ]
}
```