#!/usr/bin/env python3
"""
Create a simple test JD file for testing intervet2 endpoint.
"""

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
import os

def create_test_jd_pdf():
    """Create a simple PDF JD file for testing."""
    
    filename = "test_jd.pdf"
    
    # Create a PDF with job description content
    c = canvas.Canvas(filename, pagesize=letter)
    width, height = letter
    
    # Title
    c.setFont("Helvetica-Bold", 16)
    c.drawString(50, height - 50, "SENIOR SOFTWARE ENGINEER")
    
    # Company info
    c.setFont("Helvetica", 12)
    c.drawString(50, height - 80, "TechCorp Solutions | Bangalore, India | Full-time")
    
    # Job description
    y_position = height - 120
    
    job_content = [
        "JOB DESCRIPTION",
        "We are seeking a talented Senior Software Engineer to join our team.",
        "",
        "REQUIRED QUALIFICATIONS",
        "• 5+ years of experience in software development",
        "• Strong proficiency in Python, JavaScript, or Java",
        "• Experience with React, Node.js frameworks",
        "• Knowledge of cloud platforms (AWS, Azure, or GCP)",
        "• Experience with databases (SQL and NoSQL)",
        "• Understanding of microservices architecture",
        "• Familiarity with DevOps practices and CI/CD pipelines",
        "",
        "PREFERRED QUALIFICATIONS",
        "• Experience with machine learning and data science",
        "• Knowledge of containerization (Docker, Kubernetes)",
        "• Previous experience in a leadership role",
        "",
        "EDUCATION REQUIREMENTS",
        "• Bachelor's degree in Computer Science or related field",
        "",
        "LOCATION",
        "Bangalore, India"
    ]
    
    c.setFont("Helvetica", 10)
    for line in job_content:
        if line.startswith("•"):
            c.drawString(70, y_position, line)
        elif line.isupper() and line:
            c.setFont("Helvetica-Bold", 12)
            c.drawString(50, y_position, line)
            c.setFont("Helvetica", 10)
        else:
            c.drawString(50, y_position, line)
        y_position -= 20
        
        if y_position < 50:  # Start new page if needed
            c.showPage()
            y_position = height - 50
    
    c.save()
    print(f"✅ Created test JD file: {filename}")
    return filename

if __name__ == "__main__":
    try:
        create_test_jd_pdf()
    except ImportError:
        print("❌ reportlab not installed. Creating a simple text file instead...")
        
        # Create a simple text file as fallback
        filename = "test_jd.txt"
        with open(filename, 'w') as f:
            f.write("""SENIOR SOFTWARE ENGINEER
TechCorp Solutions | Bangalore, India | Full-time

JOB DESCRIPTION
We are seeking a talented Senior Software Engineer to join our team.

REQUIRED QUALIFICATIONS
• 5+ years of experience in software development
• Strong proficiency in Python, JavaScript, or Java
• Experience with React, Node.js frameworks
• Knowledge of cloud platforms (AWS, Azure, or GCP)
• Experience with databases (SQL and NoSQL)
• Understanding of microservices architecture
• Familiarity with DevOps practices and CI/CD pipelines

PREFERRED QUALIFICATIONS
• Experience with machine learning and data science
• Knowledge of containerization (Docker, Kubernetes)
• Previous experience in a leadership role

EDUCATION REQUIREMENTS
• Bachelor's degree in Computer Science or related field

LOCATION
Bangalore, India""")
        
        print(f"✅ Created test JD text file: {filename}")
