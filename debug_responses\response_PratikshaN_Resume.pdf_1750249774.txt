=== RAW RESPONSE ===
Length: 3178
Source: PratikshaN_Resume.pdf
Timestamp: 1750249774.0274484
=== CONTENT ===
```json
{
    "name": "<PERSON>rat<PERSON>sha Nik<PERSON>",
    "email": "<EMAIL>",
    "phone": "+918208025471",
    "education": [
        {
            "degree": "Master in Computer Application",
            "institution": "Savitribai Phule Pune University ,Pune",
            "year": "2021 -2023"
        },
        {
            "degree": "Bachelor in Computer Application",
            "institution": "Savitribai Phule Pune University Pune",
            "year": "2016 -2019"
        }
    ],
    "skills": [
        "C#.Net",
        "C++,C/CPP",
        "N Unit Test Cases",
        "Bootstrap",
        "HTML/CSS",
        "PHP",
        "Web designing",
        "Interpersonal Communication",
        "Team Work",
        "Computer Literacy"
    ],
    "experience": [
        {
            "company_name": "EvolveWare Information Technology Pvt Ltd ,Pune",
            "role": "Software Engineer",
            "duration": "November 2021 -February 2024",
            "key_responsibilities": "Convert the Legacy language to Modern language .\nPreform BRE & Business Rule Extraction , show pseudo code page\nShow diagram like Flowchart, SSD,Program Flow,BRLD etc.\n⚫ Project Name : Calculate_LOC: Used for License Generation\nFind the Number of Executable , Physical  & Commented Line of code in given\nRepository.\nDeveloped 14 Languages along with its N Unit Test Cases.\nCode optimization\nBug Fixes & Verification"
        },
        {
            "company_name": "Trainee INSC Solution LLP Pune",
            "role": "Trainee",
            "duration": "Oct 2020 - March 2021",
            "key_responsibilities": "Learn Concept on P&C Insurance Domain\nWorked on Duck Creek Administrative System\nSQL Management Tool"
        }
    ],
    "projects": [
        {
            "name": "Private Fund",
            "description": "Coding & Designing\nResponsibilities: Coding & Designing\nTechnologies Used: PHP , Bootstrap , MySQL."
        },
        {
            "name": "Photo Studio",
            "description": "Coding & Designing\nResponsibilities: Coding & Designing\nTechnologies Used: VB , MySQL."
        }
    ],
    "certifications": [
        "Microsoft. Net Course ,Hematite’s IT Training & Development Center , October2019"
    ],
    "domain_of_interest": [
        "Travelling"
    ],
    "languages_known": [
        "English",
        "Marathi",
        "Hindi"
    ],
    "achievements": [],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": "Dynamic and results-oriented Software Engineer with 2.8 years of hands-on experience in backend development. Proficient in designing, developing, and maintaining robust and scalable software solutions. Skilled in utilizing various programming languages and technologies to optimize performance and efficiency. Proven ability to collaborate effectively in cross-functional teams to deliver high-quality products on time. Passionate about continuous learning and staying updated with the latest industry trends and best practices in software development.",
    "personal_projects": [],
    "social_media": [
        "linkedin.com/in/pratiksha-nikate"
    ]
}
```