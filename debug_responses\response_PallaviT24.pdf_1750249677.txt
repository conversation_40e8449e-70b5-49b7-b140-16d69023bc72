=== RAW RESPONSE ===
Length: 3004
Source: PallaviT24.pdf
Timestamp: 1750249677.6152236
=== CONTENT ===
```json
{
    "name": "PALLAVI TANGLE",
    "email": "<EMAIL>",
    "phone": "9096743308",
    "education": [
        {
            "degree": "MCA - University of Pune",
            "institution": "University of Pune",
            "year": "2016"
        },
        {
            "degree": "B.Sc (Computer Science) - Nagpur University",
            "institution": "Nagpur University",
            "year": "2013"
        }
    ],
    "skills": [
        "C# in .NET",
        "ASP.NET MVC",
        "Entity framework",
        "Web Api",
        "SQL server 2017",
        "Postgresql",
        "IIS",
        "HTML",
        "CSS",
        "Bootstrap",
        "JQuery",
        "javascript",
        "Visual studio 2017",
        "2019",
        "Crystal Report",
        "GIT",
        "TFS (Team foundation Server)",
        "Agile methodology",
        "Service Now"
    ],
    "experience": [
        {
            "company_name": "HCL TECHNOLOGIES LTD.",
            "role": "Software Engineer",
            "duration": "4 Oct 2021 - Present",
            "key_responsibilities": "Working with Australian Digitally Operated Bank. Day to day work involves developing and adding new functionality to the customer care portal which is developed using ASP.Net Handling on call support where monitoring tools such as splunk,appdynamics,Pagerduty are used."
        },
        {
            "company_name": "INBASE TECHNOLOGY PVT. LTD.",
            "role": "Software developer",
            "duration": "17 Feb 2021 - 27 Sept 2021",
            "key_responsibilities": "To write quality code to meet customer requirements."
        },
        {
            "company_name": "PROMARC SOFTWARE Pvt. LTD.",
            "role": "Software developer",
            "duration": "23 Dec 2019 - 1 Feb 2021",
            "key_responsibilities": "Result calculation system - for Nagpur University,Wardha colleges. Createda result calculation and processing system various faculties and examsto process the  nal result for students based on their marks,grace marks, incentives and past results. Work was done using Asp.net with MS visual studio 2017 and Postgresql 14 TR - Used for report design/formatting for Tr i.e. University maintain o cial record of students.work was done using Asp.net"
        }
    ],
    "projects": [],
    "certifications": [
        "Python for Beginners - Newton School (2024)"
    ],
    "domain_of_interest": [
        "Development",
        "Learn new things related technology"
    ],
    "languages_known": [
        "English"
    ],
    "achievements": [
        "Hard worker",
        "Quick learner"
    ],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": "Software Developer with total 3+ years of expertise in designing , developing and maintaining software application. Seeking a challenging role to leverage my skills and contribute innovative projects.",
    "personal_projects": [],
    "social_media": []
}
```