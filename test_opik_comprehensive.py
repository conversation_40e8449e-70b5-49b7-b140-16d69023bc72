#!/usr/bin/env python3
"""
Comprehensive test script to verify Opik integration is working correctly.
This script tests various endpoints and functions to ensure all LLM calls and pipeline functions are being tracked.
"""

import requests
import json
import os
import time
import opik

# Configuration
API_BASE_URL = "http://localhost:8000"
TEST_RESUME_PATH = "resumes for testing/Resume-Raman Luhach.pdf"

def test_opik_configuration():
    """Test that Opik is configured correctly."""
    
    print("🧪 Testing Opik Configuration")
    print("=" * 50)
    
    try:
        # Check environment variables
        api_key = os.environ.get("OPIK_API_KEY")
        workspace = os.environ.get("OPIK_WORKSPACE")
        project = os.environ.get("OPIK_PROJECT_NAME")
        
        print(f"📊 API Key: {'✅ Set' if api_key else '❌ Missing'}")
        print(f"📊 Workspace: {workspace if workspace else '❌ Missing'}")
        print(f"📊 Project: {project if project else '❌ Missing'}")
        
        # Test Opik client initialization
        try:
            client = opik.Opik()
            print("✅ Opik client initialized successfully")
            return True
        except Exception as e:
            print(f"❌ Opik client initialization failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Opik configuration test failed: {e}")
        return False

def test_resume_parsing_with_tracking():
    """Test resume parsing endpoint to verify tracking."""
    
    print("\n🧪 Testing Resume Parsing with Opik Tracking")
    print("=" * 50)
    
    if not os.path.exists(TEST_RESUME_PATH):
        print(f"❌ Test resume not found: {TEST_RESUME_PATH}")
        return False
    
    try:
        print(f"📄 Testing with resume: {TEST_RESUME_PATH}")
        
        with open(TEST_RESUME_PATH, 'rb') as resume_file:
            files = {'file': (os.path.basename(TEST_RESUME_PATH), resume_file, 'application/pdf')}
            
            start_time = time.time()
            response = requests.post(f"{API_BASE_URL}/resume", files=files, timeout=120)
            end_time = time.time()
            
            print(f"⏱️  Request completed in {end_time - start_time:.2f} seconds")
            print(f"📊 Response status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Resume parsing successful")
                print(f"📊 Candidate: {result.get('name', 'Unknown')}")
                print(f"📊 Skills found: {len(result.get('skills', []))}")
                print(f"📊 Experience entries: {len(result.get('experience', []))}")
                return True
            else:
                print(f"❌ Resume parsing failed: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Resume parsing test failed: {e}")
        return False

def test_hybrid_resume_parsing_with_tracking():
    """Test hybrid resume parsing endpoint to verify tracking."""
    
    print("\n🧪 Testing Hybrid Resume Parsing with Opik Tracking")
    print("=" * 50)
    
    if not os.path.exists(TEST_RESUME_PATH):
        print(f"❌ Test resume not found: {TEST_RESUME_PATH}")
        return False
    
    try:
        print(f"📄 Testing hybrid parsing with resume: {TEST_RESUME_PATH}")
        
        with open(TEST_RESUME_PATH, 'rb') as resume_file:
            files = {'file': (os.path.basename(TEST_RESUME_PATH), resume_file, 'application/pdf')}
            
            start_time = time.time()
            response = requests.post(f"{API_BASE_URL}/hybrid_resume", files=files, timeout=120)
            end_time = time.time()
            
            print(f"⏱️  Request completed in {end_time - start_time:.2f} seconds")
            print(f"📊 Response status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Hybrid resume parsing successful")
                print(f"📊 Extraction method: {result.get('extraction_method', 'Unknown')}")
                print(f"📊 Candidate: {result.get('name', 'Unknown')}")
                print(f"📊 Processing time: {result.get('processing_time', 0):.2f}s")
                return True
            else:
                print(f"❌ Hybrid resume parsing failed: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Hybrid resume parsing test failed: {e}")
        return False

def test_candidate_scoring_with_tracking():
    """Test candidate scoring functionality to verify tracking."""
    
    print("\n🧪 Testing Candidate Scoring with Opik Tracking")
    print("=" * 50)
    
    try:
        # First get resume data
        print("📄 Getting resume data...")
        with open(TEST_RESUME_PATH, 'rb') as resume_file:
            files = {'file': (os.path.basename(TEST_RESUME_PATH), resume_file, 'application/pdf')}
            response = requests.post(f"{API_BASE_URL}/hybrid_resume", files=files, timeout=120)
            
            if response.status_code != 200:
                print(f"❌ Failed to get resume data: {response.text}")
                return False
            
            resume_data = response.json()
            print("✅ Resume data obtained")
        
        # Create mock JD data
        jd_data = {
            "job_title": "Senior Software Engineer",
            "company_name": "TechCorp Solutions",
            "location": "Bangalore, India",
            "required_skills": ["Python", "JavaScript", "React", "AWS", "SQL"],
            "preferred_skills": ["Machine Learning", "Docker", "Kubernetes"],
            "required_experience": "5+ years of experience in software development",
            "education_requirements": ["Bachelor's degree in Computer Science"]
        }
        
        # Test scoring with custom weightage
        print("📊 Testing candidate scoring...")
        payload = {
            "resume_json": resume_data,
            "jd_json": jd_data,
            "weightage": {
                "skills_match_direct": 5,
                "experience_match": 4,
                "reliability": 3,
                "certifications": 2,
                "location_match": 1,
                "academic_match": 3,
                "alma_mater": 0
            }
        }
        
        start_time = time.time()
        response = requests.post(
            f"{API_BASE_URL}/intervet", 
            json=payload, 
            timeout=120,
            headers={"Content-Type": "application/json"}
        )
        end_time = time.time()
        
        print(f"⏱️  Scoring completed in {end_time - start_time:.2f} seconds")
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Candidate scoring successful")
            print(f"📊 Total Score: {result.get('total_score', 0)}/100")
            print(f"📊 Fit Category: {result.get('fit_category', 'Unknown')}")
            return True
        else:
            print(f"❌ Candidate scoring failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Candidate scoring test failed: {e}")
        return False

def test_api_health():
    """Test API health."""
    
    try:
        response = requests.get(f"{API_BASE_URL}/", timeout=10)
        if response.status_code == 200:
            print("✅ API is running and healthy")
            return True
        else:
            print(f"❌ API health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Could not reach API: {e}")
        return False

def main():
    """Main test function."""
    
    print("🧪 Comprehensive Opik Integration Test Suite")
    print("=" * 60)
    print("Testing:")
    print("1. ✅ Opik configuration and client initialization")
    print("2. ✅ Resume parsing with LLM call tracking")
    print("3. ✅ Hybrid resume parsing with pipeline tracking")
    print("4. ✅ Candidate scoring with weightage tracking")
    print("5. ✅ All @track decorators functioning")
    print("=" * 60)
    
    # Check API health
    if not test_api_health():
        print("\n💡 Make sure to start the API server first:")
        print("   python main.py")
        return
    
    # Run tests
    results = []
    
    # Test Opik configuration
    results.append(test_opik_configuration())
    
    # Test resume parsing
    results.append(test_resume_parsing_with_tracking())
    
    # Test hybrid resume parsing
    results.append(test_hybrid_resume_parsing_with_tracking())
    
    # Test candidate scoring
    results.append(test_candidate_scoring_with_tracking())
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print(f"✅ Opik configuration: {'PASSED' if results[0] else 'FAILED'}")
    print(f"✅ Resume parsing tracking: {'PASSED' if results[1] else 'FAILED'}")
    print(f"✅ Hybrid parsing tracking: {'PASSED' if results[2] else 'FAILED'}")
    print(f"✅ Scoring tracking: {'PASSED' if results[3] else 'FAILED'}")
    
    overall_success = all(results)
    print(f"\n🎯 Overall Result: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    if overall_success:
        print("\n🎉 Opik integration is working correctly!")
        print("\n📋 What's Being Tracked:")
        print("   ✅ All LLM calls via get_response() function")
        print("   ✅ Resume parsing pipeline (parse_resume)")
        print("   ✅ Hybrid resume parsing (hybrid_resume_parsing_internal)")
        print("   ✅ Job description parsing (parse_jd)")
        print("   ✅ Candidate scoring (calculate_candidate_job_fit)")
        print("   ✅ Section extraction (extract_sections_regex)")
        print("   ✅ JSON structuring (parse_sections_with_gemma)")
        print("   ✅ Data normalization (normalize_resume_data)")
        print("   ✅ Interview questions generation")
        print("   ✅ JSON self-healing (llm_json_self_healing)")
        print("\n🔗 Check your traces at: https://www.comet.com/gaja-prasanth/resume-jd-matching-pipeline")
    else:
        print("\n❌ Some tests failed. Check the error messages above.")
        print("💡 Common issues:")
        print("   • API server not running")
        print("   • Opik API key not configured correctly")
        print("   • Network connectivity issues")

if __name__ == "__main__":
    main()
