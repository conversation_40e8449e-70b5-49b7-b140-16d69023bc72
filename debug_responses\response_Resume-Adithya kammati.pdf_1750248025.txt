=== RAW RESPONSE ===
Length: 3487
Source: Resume-<PERSON><PERSON><PERSON>.pdf
Timestamp: 1750248025.5947728
=== CONTENT ===
```json
{
    "name": "<PERSON><PERSON><PERSON>",
    "email": null,
    "phone": null,
    "education": [
        {
            "degree": "Bachelor of Technology (Artiﬁcial intelligence)",
            "institution": "Newton School of Technology, Rishihood University",
            "year": "2023 - 2027"
        },
        {
            "degree": "Intermediate (Class XII)",
            "institution": "Narayana Junior College",
            "year": "2020 - 2022"
        },
        {
            "degree": "Matriculation (Class X)",
            "institution": "Bhashyam High School",
            "year": "2019 - 2020"
        }
    ],
    "skills": [
        "Java",
        "JavaScript",
        "CSS",
        "HTML",
        "TypeScript",
        "Python",
        "React",
        "Mongo DB",
        "MySQL",
        "Express JS",
        "Bootstrap",
        "AngularJS",
        "NodeJS",
        "Prisma ORM",
        "Tailwind",
        "Figma",
        "Teamwork",
        "Time management",
        "Leadership"
    ],
    "experience": [
        {
            "company_name": "ShadowFox Remote",
            "role": "Cyber Tech Intern",
            "duration": "March 2024 - May 2024",
            "key_responsibilities": "Improving security and website performance. Conducted penetration tests, identiﬁed 12 vulnerabilities, reducing security risks by 30%. Enhanced website performance and redesigned the proﬁle section."
        }
    ],
    "projects": [
        {
            "name": "Anonymous Dev",
            "description": "Anonymous Dev is a dynamic platform that connects developers, enabling collaboration and career growth in a supportive community. It fosters innovation and learning through AI-driven insights and resource sharing. It also includes a skill-building hub with blogs, guides, and cheat sheets for continuous professional growth."
        },
        {
            "name": "Portfolio Website",
            "description": "A dynamic portfolio website to showcase my projects, skills, blog, and internship experience. Utilizes Aceternity UI components for a modern and consistent design."
        },
        {
            "name": "Ai Trip Planner",
            "description": "Developed an AI-powered tool for personalized travel itineraries, oering dynamic recommendations and user-preference-based optimization. Intuitive design with precise recommendations, resulting in a 15% increase in user retention.."
        }
    ],
    "certifications": [
        "Python for Beginners - Newton School (2024)",
        "Delegate, HPAIR 2024 at Harvard University",
        "Contributor, GSoC 2024"
    ],
    "domain_of_interest": [
        "AI",
        "Web Development",
        "Security"
    ],
    "languages_known": [
        "English",
        "Hindi"
    ],
    "achievements": [
        "Reducing security risks by 30%",
        "15% increase in user retention"
    ],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": "A dynamic individual with a diverse skill set in coding and design, specializing in creating intuitive digital solutions. With strong leadership and communication skills, I collaborate eectively with teams to ensure successful project outcomes. Always eager to learn and grow, I actively seek opportunities for personal and professional development.",
    "personal_projects": [],
    "social_media": [
        "linkedin.com/AdithyaKammati",
        "github.com/AdithyaKammati"
    ]
}
```