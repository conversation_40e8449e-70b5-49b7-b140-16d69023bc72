=== RAW RESPONSE ===
Length: 3790
Source: Prat<PERSON><PERSON>_<PERSON>lke_7219724155.pdf
Timestamp: 1750249743.4279542
=== CONTENT ===
```json
{
  "name": "<PERSON><PERSON><PERSON><PERSON>",
  "email": "<EMAIL>",
  "phone": "+91)7219724155",
  "education": [
    {
      "degree": "Bachelors of Engineering in Information Technology (Data Science specialization)",
      "institution": "Marathwada Mitra Mandal's College Of Engineering, Pune, Maharashtra",
      "year": "2020 - 2023"
    },
    {
      "degree": "Diploma in Information Technology",
      "institution": "Government Polytechnic Nashik, Maharashtra",
      "year": "2017 - 2020"
    }
  ],
  "skills": [
    "Java",
    "C#",
    "Python",
    "JavaScript",
    "PHP",
    "OOPS",
    "API and MYSQL",
    "Vue.js",
    "HTML",
    "CSS",
    "React.Js MVC",
    "Node.Js",
    "Laravel",
    "ASP.NET Core",
    "MySQL",
    "MongoDB",
    "Git",
    "Linux",
    "Postman",
    "Apache HTTP Server"
  ],
  "experience": [
    {
      "company_name": "Aarohinfo FI Management System",
      "role": "Software Developer",
      "duration": "March 2023 - Present",
      "key_responsibilities": "Worked on Two Dashboard Project Eduction System and CAP(Credit Appraisal Process) that uses ASP.NET,C#, Vuejs, Python Libraries ,API and MYSQL. Optimized website performance to improve page load times and enhance user Experience.Also Lead a Functional team of 10 member to successfully launch a new Peoduct line. worked individually to conceptualize the website design and the factor that how to solve the problems/tasks in the easiest and most effective way."
    },
    {
      "company_name": "Sumago Infotech Pvt.Lmt.",
      "role": "Web Developer",
      "duration": "2020-2021",
      "key_responsibilities": "Developed and maintained responsive web apps in conjunction with a diverse team to guarantee the best possible user experience on a range of devices. Played a major part in the full-stack development process, using Node.js and PHP for back-end capabilities and HTML, CSS, and JavaScript for front-end development."
    }
  ],
  "projects": [
    {
      "name": "Meals Recipe Finder",
      "description": "MealsRecipeFinder is a web-based tool that helps in finding recipes for both vegetarian and non-vegetarian meals online. It fetches 10 random meal recipes on every load. Additionally, it offers a feature to retrieve recipes by their name. Furthermore, it categorizes meals based on their initial letter and displays multiple results for a particular letter. Lastly, it can retrieve recipes based on the ingredients used in them."
    },
    {
      "name": "Drowsiness Detection And Alert System",
      "description": "Developed a Drowsiness Detection System for avoiding accidents that are due to drivers who fall asleep while driving using React-Native ,Python, Machine Learning Libraries like Face Detection, Eye Detection, Also used Dlip Librarie, EAR, MAR. Drowsiness Detection software that works on image processing using Vision library and algorithms."
    },
    {
      "name": "Automatic Timetable Generator System",
      "description": "The suggested approaches were created to address the issue of timetable generation that colleges encounter each academic year. The system has capabilities for input of the various courses, halls of lectures, departments, programs, lecturers and the specification of a few constraints from which the timetable is constructed."
    }
  ],
  "certifications": [
    "Python for Data Science provided by Cognitive class",
    "PGP – IT Management (AIMS) Enhancing my skills in the field of Software Developer (.Net Developer)",
    "HackerRank - Python (Basic)[ G i t H u b ]"
  ],
  "domain_of_interest": [
    "Data Science",
    "Web Development",
    "Machine Learning"
  ],
  "languages_known": [
    "English",
    "Marathi"
  ],
  "achievements": [
    "CGPA(Aggregate): 8.80 / 