{"event": "session_start", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "timestamp": "2025-06-25T17:16:31.459144", "message": "New API session started"}
{"event": "request_start", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "1e1fc72d-3635-48ca-8d0f-6f58802f74e9", "endpoint": "/", "timestamp": "2025-06-25T17:16:31.969965", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "1e1fc72d-3635-48ca-8d0f-6f58802f74e9", "endpoint": "/", "timestamp": "2025-06-25T17:16:31.970967", "total_time_seconds": 0.0010023117065429688, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "8022162c-f5b3-40cf-ad8e-5545b20893cd", "endpoint": "/docs", "timestamp": "2025-06-25T17:16:34.018787", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "8022162c-f5b3-40cf-ad8e-5545b20893cd", "endpoint": "/docs", "timestamp": "2025-06-25T17:16:34.018787", "total_time_seconds": 0.0, "status_code": 200, "message": "Request completed in 0.0000s with status 200"}
{"event": "request_start", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "f17738be-411b-40d5-9d8e-dcb24ce41f2b", "endpoint": "/openapi.json", "timestamp": "2025-06-25T17:16:34.194551", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "f17738be-411b-40d5-9d8e-dcb24ce41f2b", "endpoint": "/openapi.json", "timestamp": "2025-06-25T17:16:34.208583", "total_time_seconds": 0.014032125473022461, "status_code": 200, "message": "Request completed in 0.0140s with status 200"}
{"event": "request_start", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "e303f13b-2738-462b-963c-2d9374b7e1fb", "endpoint": "/intervet2", "timestamp": "2025-06-25T17:17:03.475279", "message": "Request started for endpoint: /intervet2"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "e303f13b-2738-462b-963c-2d9374b7e1fb", "endpoint": "/intervet2", "timestamp": "2025-06-25T17:17:03.484281", "endpoint_type": "intervet2", "message": "Custom metric: endpoint_type=intervet2"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "e303f13b-2738-462b-963c-2d9374b7e1fb", "endpoint": "/intervet2", "timestamp": "2025-06-25T17:17:03.527099", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "e303f13b-2738-462b-963c-2d9374b7e1fb", "endpoint": "/intervet2", "timestamp": "2025-06-25T17:17:03.528096", "file_size_bytes": 300519, "message": "Custom metric: file_size_bytes=300519"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "e303f13b-2738-462b-963c-2d9374b7e1fb", "endpoint": "/intervet2", "timestamp": "2025-06-25T17:17:03.528096", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "e303f13b-2738-462b-963c-2d9374b7e1fb", "endpoint": "/intervet2", "timestamp": "2025-06-25T17:17:03.528096", "extracted_text_length": 85, "message": "Custom metric: extracted_text_length=85"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "e303f13b-2738-462b-963c-2d9374b7e1fb", "endpoint": "/intervet2", "timestamp": "2025-06-25T17:17:03.528096", "file_processing_time": 0.03934311866760254, "message": "Custom metric: file_processing_time=0.03934311866760254"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "e303f13b-2738-462b-963c-2d9374b7e1fb", "endpoint": "/intervet2", "timestamp": "2025-06-25T17:17:03.620188", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "e303f13b-2738-462b-963c-2d9374b7e1fb", "endpoint": "/intervet2", "timestamp": "2025-06-25T17:17:03.621186", "file_size_bytes": 113383, "message": "Custom metric: file_size_bytes=113383"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "e303f13b-2738-462b-963c-2d9374b7e1fb", "endpoint": "/intervet2", "timestamp": "2025-06-25T17:17:03.621186", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "e303f13b-2738-462b-963c-2d9374b7e1fb", "endpoint": "/intervet2", "timestamp": "2025-06-25T17:17:03.621186", "extracted_text_length": 5573, "message": "Custom metric: extracted_text_length=5573"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "e303f13b-2738-462b-963c-2d9374b7e1fb", "endpoint": "/intervet2", "timestamp": "2025-06-25T17:17:03.621186", "file_processing_time": 0.09209156036376953, "message": "Custom metric: file_processing_time=0.09209156036376953"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "e303f13b-2738-462b-963c-2d9374b7e1fb", "endpoint": "/intervet2", "timestamp": "2025-06-25T17:17:03.622188", "resume_extraction_time": 0.04034018516540527, "message": "Custom metric: resume_extraction_time=0.04034018516540527"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "e303f13b-2738-462b-963c-2d9374b7e1fb", "endpoint": "/intervet2", "timestamp": "2025-06-25T17:17:03.622188", "jd_extraction_time": 0.09309005737304688, "message": "Custom metric: jd_extraction_time=0.09309005737304688"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "e303f13b-2738-462b-963c-2d9374b7e1fb", "endpoint": "/intervet2", "timestamp": "2025-06-25T17:17:03.622188", "resume_text_length": 85, "message": "Custom metric: resume_text_length=85"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "e303f13b-2738-462b-963c-2d9374b7e1fb", "endpoint": "/intervet2", "timestamp": "2025-06-25T17:17:03.622188", "jd_text_length": 5573, "message": "Custom metric: jd_text_length=5573"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "e303f13b-2738-462b-963c-2d9374b7e1fb", "endpoint": "/intervet2", "timestamp": "2025-06-25T17:17:31.346408", "resume_parsing_time": 14.582092761993408, "message": "Custom metric: resume_parsing_time=14.582092761993408"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "e303f13b-2738-462b-963c-2d9374b7e1fb", "endpoint": "/intervet2", "timestamp": "2025-06-25T17:17:31.346408", "jd_parsing_time": 13.122613191604614, "message": "Custom metric: jd_parsing_time=13.122613191604614"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "e303f13b-2738-462b-963c-2d9374b7e1fb", "endpoint": "/intervet2", "timestamp": "2025-06-25T17:17:31.351408", "evaluation_time": 0.005000114440917969, "message": "Custom metric: evaluation_time=0.005000114440917969"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "e303f13b-2738-462b-963c-2d9374b7e1fb", "endpoint": "/intervet2", "timestamp": "2025-06-25T17:17:31.351408", "total_score": 34.513888888888886, "message": "Custom metric: total_score=34.513888888888886"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "e303f13b-2738-462b-963c-2d9374b7e1fb", "endpoint": "/intervet2", "timestamp": "2025-06-25T17:17:31.351408", "fit_category": "Weak Match", "message": "Custom metric: fit_category=Weak Match"}
{"event": "request_complete", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "e303f13b-2738-462b-963c-2d9374b7e1fb", "endpoint": "/intervet2", "timestamp": "2025-06-25T17:17:31.354409", "total_time_seconds": 27.879129886627197, "status_code": 200, "message": "Request completed in 27.8791s with status 200"}
{"event": "request_start", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "e55f5e5b-281c-489e-b209-8c6424f62210", "endpoint": "/jd_parser", "timestamp": "2025-06-25T17:18:27.589439", "message": "Request started for endpoint: /jd_parser"}
{"event": "request_complete", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "e55f5e5b-281c-489e-b209-8c6424f62210", "endpoint": "/jd_parser", "timestamp": "2025-06-25T17:18:36.999562", "total_time_seconds": 9.410122871398926, "status_code": 200, "message": "Request completed in 9.4101s with status 200"}
{"event": "request_start", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "5637f7a7-2e3c-48aa-acbc-44f1a517f3f6", "endpoint": "/resume", "timestamp": "2025-06-25T17:18:39.774401", "message": "Request started for endpoint: /resume"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "5637f7a7-2e3c-48aa-acbc-44f1a517f3f6", "endpoint": "resume", "timestamp": "2025-06-25T17:18:39.776401", "message": "Custom metric: endpoint=resume"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "5637f7a7-2e3c-48aa-acbc-44f1a517f3f6", "endpoint": "/resume", "timestamp": "2025-06-25T17:18:39.776401", "file_name": "Gaja_Prasanth_Resume.pdf", "message": "Custom metric: file_name=Gaja_Prasanth_Resume.pdf"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "5637f7a7-2e3c-48aa-acbc-44f1a517f3f6", "endpoint": "/resume", "timestamp": "2025-06-25T17:18:39.776401", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "5637f7a7-2e3c-48aa-acbc-44f1a517f3f6", "endpoint": "/resume", "timestamp": "2025-06-25T17:18:39.777401", "file_size": 74312, "message": "Custom metric: file_size=74312"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "5637f7a7-2e3c-48aa-acbc-44f1a517f3f6", "endpoint": "/resume", "timestamp": "2025-06-25T17:18:39.792341", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "5637f7a7-2e3c-48aa-acbc-44f1a517f3f6", "endpoint": "/resume", "timestamp": "2025-06-25T17:18:39.792341", "file_size_bytes": 74312, "message": "Custom metric: file_size_bytes=74312"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "5637f7a7-2e3c-48aa-acbc-44f1a517f3f6", "endpoint": "/resume", "timestamp": "2025-06-25T17:18:39.792341", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "5637f7a7-2e3c-48aa-acbc-44f1a517f3f6", "endpoint": "/resume", "timestamp": "2025-06-25T17:18:39.793353", "extracted_text_length": 1823, "message": "Custom metric: extracted_text_length=1823"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "5637f7a7-2e3c-48aa-acbc-44f1a517f3f6", "endpoint": "/resume", "timestamp": "2025-06-25T17:18:39.793353", "file_processing_time": 0.014940261840820312, "message": "Custom metric: file_processing_time=0.014940261840820312"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "5637f7a7-2e3c-48aa-acbc-44f1a517f3f6", "endpoint": "/resume", "timestamp": "2025-06-25T17:18:39.793353", "text_extraction_time": 0.015951871871948242, "message": "Custom metric: text_extraction_time=0.015951871871948242"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "5637f7a7-2e3c-48aa-acbc-44f1a517f3f6", "endpoint": "/resume", "timestamp": "2025-06-25T17:18:39.793353", "extracted_text_length": 1823, "message": "Custom metric: extracted_text_length=1823"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "5637f7a7-2e3c-48aa-acbc-44f1a517f3f6", "endpoint": "/resume", "timestamp": "2025-06-25T17:18:51.479933", "parsing_time": 11.686580181121826, "message": "Custom metric: parsing_time=11.686580181121826"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "5637f7a7-2e3c-48aa-acbc-44f1a517f3f6", "endpoint": "/resume", "timestamp": "2025-06-25T17:18:51.479933", "confidence_score": 0.75, "message": "Custom metric: confidence_score=0.75"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "5637f7a7-2e3c-48aa-acbc-44f1a517f3f6", "endpoint": "/resume", "timestamp": "2025-06-25T17:18:51.479933", "fields_extracted": 18, "message": "Custom metric: fields_extracted=18"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "5637f7a7-2e3c-48aa-acbc-44f1a517f3f6", "endpoint": "/resume", "timestamp": "2025-06-25T17:18:51.479933", "skills_count": 11, "message": "Custom metric: skills_count=11"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "5637f7a7-2e3c-48aa-acbc-44f1a517f3f6", "endpoint": "/resume", "timestamp": "2025-06-25T17:18:51.479933", "education_count": 1, "message": "Custom metric: education_count=1"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "5637f7a7-2e3c-48aa-acbc-44f1a517f3f6", "endpoint": "/resume", "timestamp": "2025-06-25T17:18:51.479933", "experience_count": 3, "message": "Custom metric: experience_count=3"}
{"event": "custom_metric", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "5637f7a7-2e3c-48aa-acbc-44f1a517f3f6", "endpoint": "/resume", "timestamp": "2025-06-25T17:18:51.479933", "total_processing_time": 11.702532052993774, "message": "Custom metric: total_processing_time=11.702532052993774"}
{"event": "request_complete", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "5637f7a7-2e3c-48aa-acbc-44f1a517f3f6", "endpoint": "/resume", "timestamp": "2025-06-25T17:18:51.480933", "total_time_seconds": 11.706532716751099, "status_code": 200, "message": "Request completed in 11.7065s with status 200"}
{"event": "request_start", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "376829f3-bb9b-4c7b-9a39-c1669d865914", "endpoint": "/intervet", "timestamp": "2025-06-25T17:19:26.529057", "message": "Request started for endpoint: /intervet"}
{"event": "request_complete", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "376829f3-bb9b-4c7b-9a39-c1669d865914", "endpoint": "/intervet", "timestamp": "2025-06-25T17:19:26.532053", "total_time_seconds": 0.0029959678649902344, "status_code": 200, "message": "Request completed in 0.0030s with status 200"}
{"event": "request_start", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "23b65ff8-e9ab-4f5b-8a56-b980b4cc6ecb", "endpoint": "/intervet", "timestamp": "2025-06-25T17:20:47.625583", "message": "Request started for endpoint: /intervet"}
{"event": "request_complete", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "23b65ff8-e9ab-4f5b-8a56-b980b4cc6ecb", "endpoint": "/intervet", "timestamp": "2025-06-25T17:20:47.629585", "total_time_seconds": 0.0040018558502197266, "status_code": 200, "message": "Request completed in 0.0040s with status 200"}
{"event": "request_start", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "d59057ea-5ae0-4d82-8103-8891eaf6d663", "endpoint": "/intervet", "timestamp": "2025-06-25T17:39:55.999889", "message": "Request started for endpoint: /intervet"}
{"event": "request_complete", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "d59057ea-5ae0-4d82-8103-8891eaf6d663", "endpoint": "/intervet", "timestamp": "2025-06-25T17:39:56.002189", "total_time_seconds": 0.0022993087768554688, "status_code": 200, "message": "Request completed in 0.0023s with status 200"}
{"event": "request_start", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "03fa53de-7164-4154-a14a-eeb6dfe6c3df", "endpoint": "/intervet", "timestamp": "2025-06-25T17:40:28.107001", "message": "Request started for endpoint: /intervet"}
{"event": "request_complete", "session_id": "ddfdeb83-e4a5-4aa4-91e5-d85ac2e33c7e", "request_id": "03fa53de-7164-4154-a14a-eeb6dfe6c3df", "endpoint": "/intervet", "timestamp": "2025-06-25T17:40:28.111005", "total_time_seconds": 0.004004716873168945, "status_code": 200, "message": "Request completed in 0.0040s with status 200"}
