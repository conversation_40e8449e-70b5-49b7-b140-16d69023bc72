=== RAW RESPONSE ===
Length: 2963
Source: Resume-Anurag Pandey.pdf
Timestamp: 1750246371.47975
=== CONTENT ===
```json
{
    "name": "<PERSON><PERSON><PERSON> Pandey",
    "email": null,
    "phone": null,
    "education": [
        {
            "degree": "Bachelor of Technology (Artiﬁcial intelligence)",
            "institution": "Newton School of Technology , Rishihood University",
            "year": "2024 - 2028"
        },
        {
            "degree": "Intermediate (Class XII)",
            "institution": "FIITJEE WORLD SCHOOL NARAYANGUDA",
            "year": "2020 - 2022"
        },
        {
            "degree": "Matriculation (Class X)",
            "institution": "Army Public School Bolarum",
            "year": "2019 - 2020"
        }
    ],
    "skills": [
        "Python",
        "JavaScript",
        "CSS",
        "HTML",
        "Excel",
        "React",
        "Team Building",
        "Leadership",
        "Photoshop",
        "Social Media",
        "Graphic Design"
    ],
    "experience": [],
    "projects": [
        {
            "name": "Calculator Project",
            "description": "A web-based calculator with a user-friendly interface that supports basic arithmetic operations, updates the display dynamically, and adapts to dierent screen sizes. Features: Basic arithmetic (+, , Ö, ö), real-time display updates, responsive design, keyboard support, and error handling."
        },
        {
            "name": "Weather Website",
            "description": "A weather website that fetches real-time weather data for dierent locations, displaying temperature, conditions, and forecasts in a clean and responsive UI. Features: Search for locations, real-time weather updates, responsive design, dynamic UI, and weather condition icons."
        }
    ],
    "certifications": [
        "Python for Beginners , Newton School Of Technology ( Link ) August 2024",
        "Completed Python For Beginners Course, and it was a very good experience doing it.",
        "International Mathematics Olympiad(IMO) , Science Olympiad Foundation(SOF) ( Link ) December 2017",
        "Grand Master Level in Mental Arithmetic , ALAMA International ( Link ) January 2017"
    ],
    "achievements": [
        "I am a basketball player and have played in inter school and cluster fstate levelgmatches.",
        "I love mathematics from my childhood so thats why , I Have Completed Grand master level in mental arithmetics fAbacusgand represented in national competition two times .",
        "I have secured a silver medal in SOF IMO fScience Olympiad Foundation , International Mathematics Olympiad g"
    ],
    "volunteer_experience": [
        "Member of PR and Marketing team in synergy club."
    ],
    "references": [],
    "summary": "Aspiring developer and tech enthusiast with experience in Python, HTML, CSS, JavaScript, and React. Passionate about creating dynamic and responsive web applications while continuously learning new technologies.",
    "personal_projects": [],
    "social_media": [
        "null",
        "null"
    ]
}
```