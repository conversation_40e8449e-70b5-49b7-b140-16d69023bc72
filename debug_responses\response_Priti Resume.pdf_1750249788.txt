=== RAW RESPONSE ===
Length: 2913
Source: Priti <PERSON>sume.pdf
Timestamp: 1750249788.0667672
=== CONTENT ===
```json
{
    "name": "<PERSON><PERSON><PERSON>",
    "email": "<EMAIL>",
    "phone": "+91- 9460860884",
    "education": [
        {
            "degree": "B.Tech, Computer Science",
            "institution": "Swami Keshvanand Institute of Technology, Jaipur",
            "year": "2018 - 2022"
        }
    ],
    "skills": [
        "C#",
        ".NET Core",
        "ASP.NET",
        "Web-API",
        "MVC",
        "WPF",
        "Entity Framework Core",
        "LINQ",
        "SQL Server",
        "Git",
        "Jira",
        "Visual Studio",
        "Visual Studio Code(VS Code)",
        "Microsoft SQL Server Management Studio(SSMS)"
    ],
    "experience": [
        {
            "company_name": "In Time Tec Visionsoft Pvt. Ltd.",
            "role": "Software Engineer",
            "duration": "Feb 2022 - present",
            "key_responsibilities": "Worked well independently and within a team setting. Managed the development of a Windows-based application and a .NET Core Web API project, offering essential technical support to clients and collaborating seamlessly with colleagues. Assisting in the maintenance of code quality and addressing issues in existing functionality."
        }
    ],
    "projects": [
        {
            "name": "E-Learning System",
            "description": "Completed during the training period, this project involved developing web APIs based on a 3-tier architecture model."
        },
        {
            "name": "Sports Carnival",
            "description": "Graduation project from a learning program within the company, this console application was built from scratch. Responsibilities included creating UML diagrams, implementing 3-tier architecture, utilizing LINQ, and incorporating multithreading."
        },
        {
            "name": "Next Gen Ag Tech",
            "description": "Contributed to this company’s product, a .NET Core web API-based solution focused on agricultural technology. Worked collaboratively with the frontend team, integrating React for a user-friendly experience."
        },
        {
            "name": "HP Card Reader Configuration Utility",
            "description": "Independently worked on this existing project, a Windows-based application developed using WPF. Responsible for configuring HP card readers."
        }
    ],
    "certifications": [
        "Python for Beginners - Newton School (2024)"
    ],
    "domain_of_interest": [
        "Software Development"
    ],
    "languages_known": [
        "English",
        "Hindi"
    ],
    "achievements": [
        "Graduation project from a learning program within the company",
        "Sports Carnival"
    ],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": null,
    "personal_projects": [],
    "social_media": [
        "https://www.linkedin.com/in/priti-a-1656aa16b2"
    ]
}
```