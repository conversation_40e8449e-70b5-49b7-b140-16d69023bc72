{
    "name": "<PERSON><PERSON><PERSON>",
    "email": "siddhu7pute2000@gmail",
    "phone": "+91-7058549067",
    "education": [
        {
            "degree": "Bachelor of Technology - Btech in Computer Science and Engineering",
            "institution": "Walchand College of Engineering, Sangli-M.S.",
            "year": "August 2018 – June 2022"
        },
        {
            "degree": "Higher Secondary Certificate",
            "institution": "Musale College, Kolhapur",
            "year": "June 2016 – May 2018"
        },
        {
            "degree": "Secondary School Certificate",
            "institution": "Alphonsa School, Miraj",
            "year": "June 2016"
        }
    ],
    "skills": [
        "C#",
        "C/C++",
        "SQL (MS-SQL)",
        "TypeScript",
        "HTML",
        "CSS",
        "ASP .NET (.NET Core)",
        "Angular 14+",
        "xUnit",
        "IIS",
        "Git",
        "Github",
        "Jenkins",
        "Docker",
        "Swagger",
        "Postman",
        "Agile (Scrum/Kanban)",
        "JIRA",
        "Visual Studio",
        "Visual Studio Code",
        "Veracode",
        "SSMS"
    ],
    "experience": [
        {
            "company_name": "NICE Interactive Solutions Pvt. Ltd.",
            "role": "Associate Software Engineer",
            "duration": "July 2022 – Present",
            "key_responsibilities": "Working as Full-Stack Developer, part of the NICE CXone, Integration Hub Framework (IHF) team. This product integrates third party REST APIs and helps clients to manage/use these. The UI is based on Angular and the microservices are on .NET Core 6. Developed UI and backend for different features required for the product. Developed the major UI components for IHF. Developed functionality of FormUrlEncoded Body by creating API in IHF. Developed No Auth support functionality in IHF. Implemented UUID changes in IHF DynamoDB table. Implemented user friendly error messages on http requests made through IHF application. Developed unencrypted variables functionality in addition to encrypted secrets in IHF. Covered unit tests in IHF with unit test coverage 100 percent. Added Logs in IHF."
        },
        {
            "company_name": "NICE Interactive Solutions Pvt. Ltd.",
            "role": "Software Engineer Intern",
            "duration": "Feb 2022 – June 2022",
            "key_responsibilities": "Worked as an intern, part of the NICE CXone, Config Revamp project. Developed valid user login functionality in the application. Done practical sessions and hands on with JavaScript, Angular 13."
        }
    ],
    "projects": [
        {
            "name": "Face Mask Detector",
            "description": "Developed a hybrid model using deep and classical machine learning for face mask detection. It identifies whether the person on image/video stream is wearing a face mask or not. Open CV, TensorFlow, Python Dec 2020"
        },
        {
            "name": "Real Estate Investment",
            "description": "This system enables transparency and allows anyone to be able to invest in Real Estate assets from anywhere in the world. The investor is given platform and ERC20 Tokens are sold to the investor and funds are raised. This platform then uses the raised funds to buy real estate properties and the are put up for rent. Blockchain, Firebase, Vue JS, Stripe May 2022"
        },
        {
            "name": "Predicting Used Vehicle Prices",
            "description": "Developed a model which can accurately predict price of used vehicle. Features like manufacturer, condition, fuel type, transmission, purchased year, odometer, etc are used to test the model. sklearn | Python May 2021"
        }