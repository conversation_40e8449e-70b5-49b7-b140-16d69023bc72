================================================================================
FINAL EXTRACTION RESULTS SUMMARY
================================================================================
Extraction Method: multiple_calls
Completed: 2025-06-17T17:39:44.767994
Processing Time: 14.063202381134033 seconds
Total LLM Calls: 8
Overall Confidence: 0.9571428571428572
Sections Found: 7
================================================================================

SECTION EXTRACTION RESULTS:
----------------------------------------
SUMMARY: ✅ SUCCESS
  Confidence: 0.90
  Content Length: 156 characters
  Preview: Full stack developer with a problem-solving mindset and strong skills in frontend, backend and DSA, ...

EDUCATION: ✅ SUCCESS
  Confidence: 0.90
  Content Length: 120 characters
  Preview: Bachelor of Technology (Computer Science) 2023 - 2027
Newton School of Technology, Rishihood Univers...

EXPERIENCE: ✅ SUCCESS
  Confidence: 1.00
  Content Length: 624 characters
  Preview: WORK EXPERIENCE
Frontend Intern June 2024 - August 2024
IIT Roorkee with Social Studies Foundation R...

SKILLS: ✅ SUCCESS
  Confidence: 1.00
  Content Length: 214 characters
  Preview: Computer Languages: Java, JavaScript, CSS, HTML, TypeScript, Python
Software Packages: React, MySQL,...

PROJECTS: ✅ SUCCESS
  Confidence: 0.90
  Content Length: 940 characters
  Preview: Expedition- Backend Python , ( Github ) ( Demo ) December 2024
Tech Stack: Python
Description: Bac...

CERTIFICATIONS: ❌ FAILED
  Confidence: 0.00
  Content Length: 9 characters
  Content: NOT_FOUND

ACHIEVEMENTS: ✅ SUCCESS
  Confidence: 1.00
  Content Length: 56 characters
  Content: ACHIEVEMENTS
Grade: 8.57/10.0
Grade: 94.26%
Grade: 93.4%

LANGUAGES: ✅ SUCCESS
  Confidence: 1.00
  Content Length: 67 characters
  Content: Computer Languages: Java, JavaScript, CSS, HTML, TypeScript, Python

================================================================================
All individual LLM calls are logged in separate files in this folder.
Check the numbered call files for detailed prompt/response logs.
